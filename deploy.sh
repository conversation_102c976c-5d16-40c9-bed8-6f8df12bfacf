#!/bin/bash

# Exit on any error
set -e

# Required environment variables
REQUIRED_VARS=(
  "IMAGE_NAME"
  "TAG"
  "CONTAINER_REGISTRY_NAME"
  "ENVIRONMENT"
)

# Validate required environment variables
echo "🔍 Validating environment variables..."
for var in "${REQUIRED_VARS[@]}"; do
  if [ -z "${!var}" ]; then
    echo "❌ Error: $var environment variable must be set."
    exit 1
  fi
done

echo "✅ All required environment variables are set"
echo "📋 Environment: $ENVIRONMENT"
echo "🏷️  Image: $IMAGE_NAME"
echo "🔖 Tag: $TAG"

# Login to Azure Container Registry
echo "🔐 Logging in to Azure Container Registry..."
az acr login --name "${CONTAINER_REGISTRY_NAME}"

# Build Docker image with multiple tags based on environment
echo "🏗️  Building Docker image..."
TAGS=()

# Always tag with build number
TAGS+=("-t" "${IMAGE_NAME}:${TAG}")

# Environment-specific tagging strategy
case "${ENVIRONMENT}" in
  "production")
    TAGS+=("-t" "${IMAGE_NAME}:latest")
    TAGS+=("-t" "${IMAGE_NAME}:production")
    ;;
  "staging")
    TAGS+=("-t" "${IMAGE_NAME}:staging")
    ;;
  "development")
    TAGS+=("-t" "${IMAGE_NAME}:develop")
    ;;
  *)
    echo "⚠️  Unknown environment: $ENVIRONMENT, using default tagging"
    TAGS+=("-t" "${IMAGE_NAME}:${ENVIRONMENT}")
    ;;
esac

# Build the image
docker build "${TAGS[@]}" -f ./Dockerfile .

# Push all tags
echo "📤 Pushing Docker images..."
for tag in $(docker images --format "table {{.Repository}}:{{.Tag}}" | grep "^${IMAGE_NAME}:" | grep -v "REPOSITORY"); do
  echo "  📤 Pushing: $tag"
  docker push "$tag"
done

echo "✅ Docker image deployment completed successfully!"