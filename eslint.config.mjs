import stylistic from '@stylistic/eslint-plugin'
import importPlugin from 'eslint-plugin-import';
import jsdoc from 'eslint-plugin-jsdoc'
import sortClassMembers from 'eslint-plugin-sort-class-members'
import globals from 'globals'
import eslint from '@eslint/js'
import tseslint from 'typescript-eslint'
import unicorn from 'eslint-plugin-unicorn'
import noCommentsInFunctions from './eslint-rules/no-comments-in-functions.mjs'
 
export default [
  ...tseslint.config(
    {
      ignores: ['eslint.config.mjs', 'src/metadata.ts', 'generated/**'],
    },
    eslint.configs.recommended,
    tseslint.configs.recommendedTypeChecked,
    tseslint.configs.stylisticTypeChecked,
    {
      files: ['**/*.{ts,tsx}'],
      extends: [
        importPlugin.flatConfigs.recommended,
        importPlugin.flatConfigs.typescript,
      ],
    }
  ),
  unicorn.configs['recommended'],
  sortClassMembers.configs['flat/recommended'],
  stylistic.configs['recommended'],
  stylistic.configs['disable-legacy'],
  jsdoc.configs['flat/recommended-typescript'],
  {
    files: ['**/*.{ts,tsx}'],
    plugins: { 
      '@stylistic': stylistic,
      '@typescript-eslint': tseslint.plugin,
      'local': {
        rules: {
          'no-comments-in-functions': noCommentsInFunctions
        }
      }
    },
    linterOptions: {
      noInlineConfig: true,
      reportUnusedDisableDirectives: "error",
      reportUnusedInlineConfigs: "error",
    },
 
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.jest,
      },
 
      parser: tseslint.parser,
      ecmaVersion: 5,
      sourceType: 'module',
 
      parserOptions: {
        projectService: true,
        tsconfigRootDir: import.meta.dirname,
        project: ['tsconfig.lint.json'],
      },
    },
 
    settings: {
      'import/resolver': {
        typescript: {
          alwaysTryTypes: true,
          project: ['./tsconfig.lint.json'],
        },
        node: true,
      },
      'import/parsers': {
        '@typescript-eslint/parser': ['.ts', '.tsx'],
      },
    },
 
    rules: {
      '@stylistic/quotes': ['error', 'double'],
      '@stylistic/semi': ['error', 'always'],
 
      'camelcase': [
        'error',
        {
          ignoreDestructuring: true,
        },
      ],
      'capitalized-comments': ['error', 'always'],
 
      'complexity': [
        'error',
        {
          max: 30,
        },
      ],
 
      'default-case': 'error',
 
      'eqeqeq': ['error', 'smart'],
      'guard-for-in': 'error',
      'id-match': 'error',
 
      'import/no-default-export': 'off',
      'import/no-deprecated': 'warn',
      'import/no-unresolved': [
        "error", {
          'ignore': ['zone\\.js/.*']
        }
      ],
      'import/order': [
        'error',
        {
          alphabetize: {
            caseInsensitive: false,
            order: 'asc',
          },
        },
      ],
 
      'max-classes-per-file': ['error', 1],
 
      'max-lines': [
        'warn',
        {
          max: 1000,
          skipBlankLines: true,
          skipComments: true,
        },
      ],
 
      'no-bitwise': 'error',
      'no-caller': 'error',
 
      'no-console': [
        'error',
        {
          allow: [
            'dirxml',
            'warn',
            'error',
            'dir',
            'timeLog',
            'assert',
            'clear',
            'count',
            'countReset',
            'group',
            'groupCollapsed',
            'groupEnd',
            'table',
            'Console',
            'markTimeline',
            'profile',
            'profileEnd',
            'timeline',
            'timelineEnd',
            'timeStamp',
            'context',
          ],
        },
      ],
 
      'no-eval': 'error',
      'no-extra-bind': 'error',
      'no-invalid-this': 'off',
      'no-new-func': 'error',
      'no-new-wrappers': 'error',
      'no-param-reassign': 'error',
 
      'no-restricted-imports': [
        'error',
        {
          paths: ['rxjs/Rx'],
          patterns: ['rxjs/internal/*'],
        },
      ],
 
      'no-restricted-syntax': ['error', 'ForInStatement'],
 
      'no-shadow': [
        'error',
        {
          hoist: 'all',
        },
      ],
 
      'no-template-curly-in-string': 'error',
      'no-throw-literal': 'error',
      'no-undef-init': 'error',
 
      'no-underscore-dangle': [
        'error',
        {
          allow: ['_count'],
          allowAfterThis: true,
        },
      ],
 
      'no-useless-constructor': 'off',
      'no-var': 'error',
      'no-void': 'error',
      'one-var': ['error', 'never'],
      'prefer-const': 'error',
      'prefer-object-spread': 'error',
      'prefer-template': 'error',
      'radix': 'error',
 
      'sort-class-members/sort-class-members': [
        2,
        {
          order: [
            '[static-properties]',
            '[static-methods]',
            '[properties]',
            '[conventional-private-properties]',
            'constructor',
            '[methods]',
            '[conventional-private-methods]',
          ],
          accessorPairPositioning: "getThenSet",
        },
      ],
 
      "sort-keys": [
        "error",
        "asc",
        {
          caseSensitive: true,
          natural: true,
          minKeys: 2,
        },
      ],
      'sort-vars': 'error',
      'yoda': 'error',
 
      'local/no-comments-in-functions': 'error',
 
      "@typescript-eslint/ban-ts-comment": "error",
      "@typescript-eslint/consistent-type-assertions": "error",
      "@typescript-eslint/consistent-type-definitions": "error",
      "@typescript-eslint/dot-notation": "off",
      "@typescript-eslint/explicit-member-accessibility": [
        "error",
        {
          accessibility: "explicit",
          overrides: {
            accessors: "explicit",
            constructors: "explicit",
            parameterProperties: "explicit",
          },
        },
      ],
      "@typescript-eslint/interface-name-prefix": "off",
      "@typescript-eslint/naming-convention": [
        "error",
        {
          selector: "default",
          format: ["camelCase"],
        },
        {
          selector: "variableLike",
          format: ["camelCase"],
        },
        {
          selector: "variable",
          format: ["camelCase", "UPPER_CASE"],
        },
        {
          selector: "parameter",
          format: ["camelCase"],
          leadingUnderscore: "allow",
        },
        {
          selector: "memberLike",
          format: ["camelCase"],
        },
        {
          selector: "memberLike",
          modifiers: ["private"],
          format: ["camelCase"],
          leadingUnderscore: "allow",
        },
        {
          selector: "enum",
          format: ["UPPER_CASE"],
        },
        {
          selector: "enumMember",
          format: ["UPPER_CASE"],
        },
        {
          selector: "typeLike",
          format: ["PascalCase"],
        },
        {
          selector: "typeParameter",
          format: ["PascalCase"],
          prefix: ["T"],
        },
      ],
      "@typescript-eslint/no-empty-function": "off",
      "@typescript-eslint/no-empty-interface": "error",
      "@typescript-eslint/no-for-in-array": "error",
      "@typescript-eslint/no-inferrable-types": "error",
      "@typescript-eslint/no-misused-new": "error",
      "@typescript-eslint/no-non-null-assertion": "error",
      "@typescript-eslint/no-require-imports": "error",
      "@typescript-eslint/no-this-alias": "error",
      "@typescript-eslint/no-unnecessary-boolean-literal-compare": "error",
      "@typescript-eslint/no-unnecessary-type-arguments": "error",
      "@typescript-eslint/no-unused-expressions": "error",
      "@typescript-eslint/prefer-function-type": "error",
      "@typescript-eslint/prefer-readonly": "error",
      "@typescript-eslint/restrict-plus-operands": "error",
      "@typescript-eslint/strict-boolean-expressions": "off",
      "@typescript-eslint/triple-slash-reference": [
        "error",
        {
          path: "always",
          types: "prefer-import",
          lib: "always",
        },
      ],
      "@typescript-eslint/unbound-method": [
        "error",
        {
          ignoreStatic: true,
        },
      ],
      "@typescript-eslint/unified-signatures": "error",
 
      camelcase: [
        "error",
        {
          ignoreDestructuring: true,
        },
      ],
    },
  },
  {
    files: ['**/*.js'],
    extends: [tseslint.configs.disableTypeChecked],
  },
  {
    files: ['src/env.js'],
    rules: {
      'unicorn/prevent-abbreviations': 'off'
    }
  },
  {
    ignores: [
      "src/assets/**/*.json",
      "src/test.ts",
      "src/polyfills.ts",
      "src/assets/css/*.css",
      "src/assets/js/*.js",
    ]
  },
]