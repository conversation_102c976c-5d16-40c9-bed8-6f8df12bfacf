# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is a RESTful API for Materiact, a material management and formulation system.

- **Framework**: NestJS with Fastify
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Azure AD OAuth2
- **Testing**: Jest for unit and E2E tests

## Project Structure

The backend is a standard NestJS project. The core application logic is located in the `src/` directory, organized by feature modules.

- `src/`: Contains all the source code.
  - `src/{feature}/`: Each folder like `user`, `material`, `formulation`, etc., is a self-contained NestJS module that handles a specific domain.
  - `src/main.ts`: The application entry point.
  - `src/common/`: Shared components, decorators, and utilities.
  - `src/auth/`: Handles authentication and authorization using Azure AD.
- `prisma/`: Contains the Prisma schema (`schema.prisma`) for database models and migrations.
- `test/`: Contains end-to-end tests. Unit tests (`.spec.ts`) are co-located with the source files in `src/`.

## Common Commands

- **Install dependencies**: `npm install`
- **Run in development mode**: `npm run start:dev` (This also runs `prisma generate`)
- **Lint code**: `npm run lint`
- **Fix linting errors**: `npm run lint:fix`
- **Run unit tests**: `npm run test`
- **Run E2E tests**: `npm run test:e2e`
- **Build for production**: `npm run build`
- **Run a single test**: `npm test -- <path-to-test-file>`

## Development Workflow

1.  **Environment Setup**: Copy `.env.example` to `.env` and fill in the required values for the database and Azure AD credentials.
2.  **Database Changes**: To alter the database schema, edit `prisma/schema.prisma`. After making changes, run `npx prisma generate` to update the Prisma Client and `npx prisma db push` to sync the changes with the database.
3.  **Adding Features**: When adding a new feature, create a new module using the Nest CLI (`nest g module my-feature`). Follow the existing modular structure.
4.  **Testing**: For any new logic, add unit tests in a `.spec.ts` file next to the source file. For new endpoints, add E2E tests in the `test/` directory.
5.  **API Documentation**: While the application is running, you can access the Swagger API documentation at `http://localhost:3000/api-docs`.

## Database Architecture

The system manages materials with complex polymorphic relationships:

- **Material**: Core entity with different families (POLYMERS, FILLERS, ELASTOMERS, ADDITIVES, RECYCLE_POLYMERS)
- **Formulation**: Recipes combining multiple materials with percentages
- **Request**: Access requests for formulations with approval workflow
- **User Management**: Role-based access with departments and locations
- **Notifications**: Event-driven system for status changes

Key relationships:
- Materials have family-specific properties via separate tables (Polymer, Filler, etc.)
- Formulations link to materials through FormulationMaterial junction table
- Test results are stored separately and linked to formulations
- Property metadata defines structure for material family properties

## Authentication & Authorization

- **Guard System**: Uses `AuthGuard` for token validation and `RoleGuard` for permissions
- **Development Mode**: Set `BYPASS_AUTH=true` in development to skip authentication
- **Token Flow**: Azure AD OAuth2 tokens validated via `AuthService`
- **User Context**: Authenticated requests include user info via `AuthenticatedRequest` interface

## Code Quality Rules

- **No Comments in Functions**: Custom ESLint rule prevents comments inside function bodies
- **Lint & Format**: Run `npm run lint:fix` to fix both ESLint issues and format Prisma schema
- **Path Aliases**: Use `@/` prefix for imports (e.g., `@/common`, `@/auth`)
- **Repository Pattern**: Each module follows repository pattern with separate service/repository layers

## Module Architecture

Each feature module follows consistent structure:
- **Controller**: HTTP endpoints and request/response handling
- **Service**: Business logic and orchestration  
- **Repository**: Data access layer with Prisma queries
- **DTOs**: Request/response data transfer objects
- **Events**: Domain events for inter-module communication

## Testing Strategy

- **Unit Tests**: Co-located `.spec.ts` files test individual components
- **E2E Tests**: Full request/response cycle testing in `test/` directory
- **Coverage**: Run `npm run test:cov` to generate coverage reports
- **Isolated Testing**: Each module can be tested independently

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.