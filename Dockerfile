FROM node:22-alpine AS builder

WORKDIR /usr/src/app

COPY package*.json ./
RUN npm ci

COPY . .
RUN npx prisma generate
RUN npm run build

FROM node:22-alpine

WORKDIR /usr/src/app

# Add security: run as non-root user
RUN chown -R node:node /usr/src/app
USER node

COPY --from=builder --chown=node:node /usr/src/app/package*.json ./
COPY --from=builder --chown=node:node /usr/src/app/dist ./dist
COPY --from=builder --chown=node:node /usr/src/app/node_modules ./node_modules
COPY --from=builder --chown=node:node /usr/src/app/generated/prisma ./generated/prisma
COPY --from=builder --chown=node:node /usr/src/app/prisma ./prisma

# Remove the production npm install since we're copying all modules
# RUN npm ci --only=production

EXPOSE 3000

# Use environment variable for port
ENV PORT=3000
ENV NODE_ENV=production

CMD ["node", "dist/main"]