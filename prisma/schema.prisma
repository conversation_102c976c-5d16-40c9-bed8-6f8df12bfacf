generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Role {
  id    String @id @default(uuid())
  name  String @unique
  code  String @unique
  users User[]

  @@map("role")
}

model Location {
  id      String @id @default(uuid())
  city    String
  country String
  users   User[]

  @@unique([city, country])
  @@map("location")
}

model Department {
  id    String @id @default(uuid())
  name  String @unique
  users User[]

  @@map("department")
}

model Supplier {
  id        String     @id @default(uuid())
  name      String     @unique
  materials Material[]

  @@map("supplier")
}

model User {
  id            String         @id @default(uuid())
  name          String
  email         String         @unique
  locationId    String         @map("location_id")
  departmentId  String         @map("department_id")
  roleId        String         @map("role_id")
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @default(now()) @updatedAt @map("updated_at")
  formulations  Formulation[]  @relation("FormulationOwner")
  requests      Request[]      @relation("RequestRequester")
  notifications Notification[]
  department    Department     @relation(fields: [departmentId], references: [id])
  location      Location       @relation(fields: [locationId], references: [id])
  role          Role           @relation(fields: [roleId], references: [id])

  @@map("user")
}

model Material {
  id                   String                @id @default(uuid())
  type                 String                @map("material_type")
  reference            String                @map("material_reference")
  family               MaterialFamily
  supplierId           String                @map("supplier_id")
  supplierBatchNumber  String                @map("supplier_batch_number")
  origin               String                @map("material_origin")
  status               MaterialStatus        @default(AVAILABLE)
  additive             Additive?
  elastomer            Elastomer?
  filler               Filler?
  formulationMaterials FormulationMaterial[]
  supplier             Supplier              @relation(fields: [supplierId], references: [id])
  polymer              Polymer?
  recyclePolymer       RecyclePolymer?

  @@map("material")
}

model Polymer {
  id                        String    @id @default(uuid())
  materialId                String    @unique @map("material_id")
  resultUpdateDate          DateTime? @map("result_update_date")
  validationEngineering     String?   @map("validation_engineering")
  tds                       String?   @map("tds")
  priceExw                  Float?    @map("price_exw")
  priceExwDate              String?   @map("price_exw_date")
  comment                   String?   @map("comment")
  mfiNorme                  String?   @map("mfi_norme")
  mfiTestConditions         String?   @map("mfi_test_conditions")
  mfiAv                     Float?    @map("mfi_av")
  mfiStdDv                  Float?    @map("mfi_std_dv")
  densityNorme              String?   @map("density_norme")
  densityAv                 Float?    @map("density_av")
  densityStdDv              Float?    @map("density_std_dv")
  tensileModulusNorme       String?   @map("tensile_modulus_norme")
  tensileModulusCond        String?   @map("tensile_modulus_conditions")
  tensileModulusAv          Float?    @map("tensile_modulus_av")
  tensileModulusStdDv       Float?    @map("tensile_modulus_std_dv")
  flexuralModulusNorme      String?   @map("flexural_modulus_norme")
  flexuralModulusAv         Float?    @map("flexural_modulus_av")
  flexuralModulusStdDev     Float?    @map("flexural_modulus_std_dev")
  flexuralStressFcAv        Float?    @map("flexural_stress_fc_av")
  flexuralStressFcStdDev    Float?    @map("flexural_stress_fc_std_dev")
  stressBreakNorme          String?   @map("stress_break_norme")
  stressBreakAv             Float?    @map("stress_break_av")
  stressBreakStdDv          Float?    @map("stress_break_std_dv")
  stressYieldAv             Float?    @map("stress_yield_av")
  stressYieldStdDv          Float?    @map("stress_yield_std_dv")
  yieldStrainAv             Float?    @map("yield_strain_av")
  yieldStrainStdDv          Float?    @map("yield_strain_std_dv")
  strainBreakNorme          String?   @map("strain_break_norme")
  strainBreakAv             Float?    @map("strain_break_av")
  strainBreakStdDv          Float?    @map("strain_break_std_dv")
  nominalStrainBreakAv      Float?    @map("nominal_strain_break_av")
  nominalStrainBreakStdDv   Float?    @map("nominal_strain_break_std_dv")
  notchedIzodNorme          String?   @map("notched_izod_norme")
  notchedIzodAv             Float?    @map("notched_izod_av")
  notchedIzodStdDv          Float?    @map("notched_izod_std_dv")
  notchedIzodFailureType    String?   @map("notched_izod_failure_type")
  unnotchedIzodNorme        String?   @map("unnotched_izod_norme")
  unnotchedIzodAv           Float?    @map("unnotched_izod_av")
  unnotchedIzodStdDv        Float?    @map("unnotched_izod_std_dv")
  unnotchedIzodFailureType  String?   @map("unnotched_izod_failure_type")
  hdtBNorme                 String?   @map("hdt_b_norme")
  hdtBAv                    Float?    @map("hdt_b_av")
  hdtBStdDv                 Float?    @map("hdt_b_std_dv")
  cma23Norm                 String?   @map("cma_23_norm")
  cma23V                    Float?    @map("cma_23_v")
  cma23NbSamples            Int?      @map("cma_23_nb_samples")
  cma23MeanBreakType        String?   @map("cma_23_mean_break_type")
  cma23EnergyForceMaxAv     Float?    @map("cma_23_energy_force_max_av")
  cma23EnergyForceMaxStdDev Float?    @map("cma_23_energy_force_max_std_dev")
  cma23EnergyPunctureAv     Float?    @map("cma_23_energy_puncture_av")
  cma23EnergyPunctureStdDev Float?    @map("cma_23_energy_puncture_std_dev")
  cma0Norm                  String?   @map("cma_0_norm")
  cma0V                     Float?    @map("cma_0_v")
  cma0NbSamples             Int?      @map("cma_0_nb_samples")
  cma0MeanBreakType         String?   @map("cma_0_mean_break_type")
  cma0EnergyForceMaxAv      Float?    @map("cma_0_energy_force_max_av")
  cma0EnergyForceMaxStdDev  Float?    @map("cma_0_energy_force_max_std_dev")
  cma0EnergyPunctureAv      Float?    @map("cma_0_energy_puncture_av")
  cma0EnergyPunctureStdDev  Float?    @map("cma_0_energy_puncture_std_dev")
  cma10Norm                 String?   @map("cma_minus_10_norm")
  cma10V                    Float?    @map("cma_minus_10_v")
  cma10NbSamples            Int?      @map("cma_minus_10_nb_samples")
  cma10MeanBreakType        String?   @map("cma_minus_10_mean_break_type")
  cma10EnergyForceMaxAv     Float?    @map("cma_minus_10_energy_force_max_av")
  cma10EnergyForceMaxStdDev Float?    @map("cma_minus_10_energy_force_max_std_dev")
  cma10EnergyPunctureAv     Float?    @map("cma_minus_10_energy_puncture_av")
  cma10EnergyPunctureStdDev Float?    @map("cma_minus_10_energy_puncture_std_dev")
  cma20Norm                 String?   @map("cma_minus_20_norm")
  cma20V                    Float?    @map("cma_minus_20_v")
  cma20NbSamples            Int?      @map("cma_minus_20_nb_samples")
  cma20MeanBreakType        String?   @map("cma_minus_20_mean_break_type")
  cma20EnergyForceMaxAv     Float?    @map("cma_minus_20_energy_force_max_av")
  cma20EnergyForceMaxStdDev Float?    @map("cma_minus_20_energy_force_max_std_dev")
  cma20EnergyPunctureAv     Float?    @map("cma_minus_20_energy_puncture_av")
  cma20EnergyPunctureStdDev Float?    @map("cma_minus_20_energy_puncture_std_dev")
  cma30Norm                 String?   @map("cma_minus_30_norm")
  cma30V                    Float?    @map("cma_minus_30_v")
  cma30NbSamples            Int?      @map("cma_minus_30_nb_samples")
  cma30MeanBreakType        String?   @map("cma_minus_30_mean_break_type")
  cma30EnergyForceMaxAv     Float?    @map("cma_minus_30_energy_force_max_av")
  cma30EnergyForceMaxStdDev Float?    @map("cma_minus_30_energy_force_max_std_dev")
  cma30EnergyPunctureAv     Float?    @map("cma_minus_30_energy_puncture_av")
  cma30EnergyPunctureStdDev Float?    @map("cma_minus_30_energy_puncture_std_dev")
  material                  Material  @relation(fields: [materialId], references: [id])

  @@map("polymer")
}

model Filler {
  id               String   @id @default(uuid())
  materialId       String   @unique @map("material_id")
  codeanonymFiller String?  @map("codeanonym_filler")
  bet              Float?   @map("bet")
  d50              Float?   @map("d50")
  d95d05           Float?   @map("d98_d95")
  whiteness        Float?   @map("whiteness")
  form             String?  @map("form")
  material         Material @relation(fields: [materialId], references: [id])

  @@map("filler")
}

model Elastomer {
  id               String   @id @default(uuid())
  materialId       String   @unique @map("material_id")
  density          Float?   @map("density")
  mfi190_2_16      Float?   @map("mfi_190_2_16")
  codeanonymElasto String?  @map("codeanonym_elasto")
  nIzod23          Float?   @map("n_izod_23")
  flexModulus      Float?   @map("flex_modulus")
  tracModulus100   Float?   @map("trac_modulus_100")
  elongAtBreak     Float?   @map("elong_at_break")
  mfi230_2_16      Float?   @map("mfi_230_2_16")
  meltingPoint     Float?   @map("melting_point")
  hdtB             Float?   @map("hdt_b")
  hdtA             Float?   @map("hdt_a")
  shoreA           Float?   @map("shore_a")
  shoreD           Float?   @map("shore_d")
  material         Material @relation(fields: [materialId], references: [id])

  @@map("elastomer")
}

model Additive {
  id                String   @id @default(uuid())
  materialId        String   @unique @map("material_id")
  anonymizationCode String?  @map("anonymization_code")
  material          Material @relation(fields: [materialId], references: [id])

  @@map("additive")
}

model RecyclePolymer {
  id                         String    @id @default(uuid())
  materialId                 String    @unique @map("material_id")
  technicalProfileAvgValue   String?   @map("technical_profile_avg_value")
  resultUpdateDate           DateTime? @map("result_update_date")
  validationEngineering      String?   @map("validation_engineering")
  feedstockOrUseInCompounds  String?   @map("feedstock_or_use_in_compounds")
  color                      String?   @map("color")
  pirPcrElv                  String?   @map("pir_pcr_elv")
  wasteDetails               String?   @map("waste_details")
  materialForm               String?   @map("material_form")
  tds                        String?   @map("tds")
  productVolumesKtY          Float?    @map("product_volumes_kt_y")
  volumesAvailableForMactKtY Float?    @map("volumes_available_for_mact_kt_y")
  priceExw                   Float?    @map("price_exw")
  priceExwDate               String?   @map("price_exw_date")
  certificatesReachRohs      String?   @map("certificates_reach_rohs")
  endOfWasteStatus           String?   @map("end_of_waste_status")
  comment                    String?   @map("comment")
  filtrationLocation         String?   @map("filtration_location")
  systemFiltration           String?   @map("system_filtration")
  filtrationSize             String?   @map("filtration_size")
  quantityFilteredRemaining  Float?    @map("quantity_filtered_remaining")
  d22NbFiltersUsed           Int?      @map("d22_nb_filters_used")
  d22NbFilterPerKgFeedstock  Float?    @map("d22_nb_filter_per_kg_feedstock")
  d32QuantityScrap           Float?    @map("d32_quantity_scrap")
  d32NbFilterPerKgFeedstock  Float?    @map("d32_nb_filter_per_kg_feedstock")
  levelOfPollution           String?   @map("level_of_pollution")
  venting                    String?   @map("venting")
  trialDate                  DateTime? @map("trial_date")
  vacuumPressure             String?   @map("vacuum_pressure")
  screwSpeed                 String?   @map("screw_speed")
  screwProfile               String?   @map("screw_profile")
  mfiNorme                   String?   @map("mfi_norme")
  mfiTestConditions          String?   @map("mfi_test_conditions")
  mfiAv                      Float?    @map("mfi_av")
  mfiStdDv                   Float?    @map("mfi_std_dv")
  ashContentNorme            String?   @map("ash_content_norme")
  ashContentAv               Float?    @map("ash_content_av")
  ashContentStdDv            Float?    @map("ash_content_std_dv")
  densityNorme               String?   @map("density_norme")
  densityAv                  Float?    @map("density_av")
  densityStdDv               Float?    @map("density_std_dv")
  tensileModulusNorme        String?   @map("tensile_modulus_norme")
  tensileModulusConditions   String?   @map("tensile_modulus_conditions")
  tensileModulusAv           Float?    @map("tensile_modulus_av")
  tensileModulusStdDv        Float?    @map("tensile_modulus_std_dv")
  flexuralModulusNorme       String?   @map("flexural_modulus_norme")
  flexuralModulusAv          Float?    @map("flexural_modulus_av")
  flexuralModulusStdDev      Float?    @map("flexural_modulus_std_dev")
  flexuralStressFcAv         Float?    @map("flexural_stress_fc_av")
  flexuralStressFcStdDev     Float?    @map("flexural_stress_fc_std_dev")
  stressBreakNorme           String?   @map("stress_break_norme")
  stressBreakAv              Float?    @map("stress_break_av")
  stressBreakStdDv           Float?    @map("stress_break_std_dv")
  stressYieldAv              Float?    @map("stress_yield_av")
  stressYieldStdDv           Float?    @map("stress_yield_std_dv")
  yieldStrainAv              Float?    @map("yield_strain_av")
  yieldStrainStdDv           Float?    @map("yield_strain_std_dv")
  strainBreakNorme           String?   @map("strain_break_norme")
  strainBreakAv              Float?    @map("strain_break_av")
  strainBreakStdDv           Float?    @map("strain_break_std_dv")
  nominalStrainBreakAv       Float?    @map("nominal_strain_break_av")
  nominalStrainBreakStdDv    Float?    @map("nominal_strain_break_std_dv")
  notchedIzodNorme           String?   @map("notched_izod_norme")
  notchedIzodAv              Float?    @map("notched_izod_av")
  notchedIzodStdDv           Float?    @map("notched_izod_std_dv")
  notchedIzodFailureType     String?   @map("notched_izod_failure_type")
  unnotchedIzodNorme         String?   @map("unnotched_izod_norme")
  unnotchedIzodAv            Float?    @map("unnotched_izod_av")
  unnotchedIzodStdDv         Float?    @map("unnotched_izod_std_dv")
  unnotchedIzodFailureType   String?   @map("unnotched_izod_failure_type")
  hdtBNorme                  String?   @map("hdt_b_norme")
  hdtBAv                     Float?    @map("hdt_b_av")
  hdtBStdDv                  Float?    @map("hdt_b_std_dv")
  odorNorme                  String?   @map("odor_norme")
  odorNote1                  String?   @map("odor_note_1")
  odorNote2                  String?   @map("odor_note_2")
  odorNote3                  String?   @map("odor_note_3")
  odorAv                     Float?    @map("odor_av")
  odorStdv                   Float?    @map("odor_stdv")
  vocFogNorme                String?   @map("voc_fog_norme")
  voc                        Float?    @map("voc")
  voc2                       Float?    @map("voc2")
  fog                        Float?    @map("fog")
  l                          Float?    @map("l")
  a                          Float?    @map("a")
  b                          Float?    @map("b")
  cma23Norm                  String?   @map("cma_23_norm")
  cma23V                     Float?    @map("cma_23_v")
  cma23NbSamples             Int?      @map("cma_23_nb_samples")
  cma23MeanBreakType         String?   @map("cma_23_mean_break_type")
  cma23EnergyForceMaxAv      Float?    @map("cma_23_energy_force_max_av")
  cma23EnergyForceMaxStdDev  Float?    @map("cma_23_energy_force_max_std_dev")
  cma23EnergyPunctureAv      Float?    @map("cma_23_energy_puncture_av")
  cma23EnergyPunctureStdDev  Float?    @map("cma_23_energy_puncture_std_dev")
  cma0Norm                   String?   @map("cma_0_norm")
  cma0V                      Float?    @map("cma_0_v")
  cma0NbSamples              Int?      @map("cma_0_nb_samples")
  cma0MeanBreakType          String?   @map("cma_0_mean_break_type")
  cma0EnergyForceMaxAv       Float?    @map("cma_0_energy_force_max_av")
  cma0EnergyForceMaxStdDev   Float?    @map("cma_0_energy_force_max_std_dev")
  cma0EnergyPunctureAv       Float?    @map("cma_0_energy_puncture_av")
  cma0EnergyPunctureStdDev   Float?    @map("cma_0_energy_puncture_std_dev")
  cma10Norm                  String?   @map("cma_minus_10_norm")
  cma10V                     Float?    @map("cma_minus_10_v")
  cma10NbSamples             Int?      @map("cma_minus_10_nb_samples")
  cma10MeanBreakType         String?   @map("cma_minus_10_mean_break_type")
  cma10EnergyForceMaxAv      Float?    @map("cma_minus_10_energy_force_max_av")
  cma10EnergyForceMaxStdDev  Float?    @map("cma_minus_10_energy_force_max_std_dev")
  cma10EnergyPunctureAv      Float?    @map("cma_minus_10_energy_puncture_av")
  cma10EnergyPunctureStdDev  Float?    @map("cma_minus_10_energy_puncture_std_dev")
  cma20Norm                  String?   @map("cma_minus_20_norm")
  cma20V                     Float?    @map("cma_minus_20_v")
  cma20NbSamples             Int?      @map("cma_minus_20_nb_samples")
  cma20MeanBreakType         String?   @map("cma_minus_20_mean_break_type")
  cma20EnergyForceMaxAv      Float?    @map("cma_minus_20_energy_force_max_av")
  cma20EnergyForceMaxStdDev  Float?    @map("cma_minus_20_energy_force_max_std_dev")
  cma20EnergyPunctureAv      Float?    @map("cma_minus_20_energy_puncture_av")
  cma20EnergyPunctureStdDev  Float?    @map("cma_minus_20_energy_puncture_std_dev")
  cma30Norm                  String?   @map("cma_minus_30_norm")
  cma30V                     Float?    @map("cma_minus_30_v")
  cma30NbSamples             Int?      @map("cma_minus_30_nb_samples")
  cma30MeanBreakType         String?   @map("cma_minus_30_mean_break_type")
  cma30EnergyForceMaxAv      Float?    @map("cma_minus_30_energy_force_max_av")
  cma30EnergyForceMaxStdDev  Float?    @map("cma_minus_30_energy_force_max_std_dev")
  cma30EnergyPunctureAv      Float?    @map("cma_minus_30_energy_puncture_av")
  cma30EnergyPunctureStdDev  Float?    @map("cma_minus_30_energy_puncture_std_dev")
  material                   Material  @relation(fields: [materialId], references: [id])

  @@map("recycle_polymer")
}

model Extrusion {
  id           String @id @default(uuid()) @map("id_ext")
  screwProfile String @map("screw_profile")
  throughput   Float  @map("throughput")
  motorRpm     Float  @map("motor_rpm")
  screwDiam    Float  @map("screw_diam")
  lDRation     Float  @map("l_d_ration")
  tZone1       Float  @map("t_zone_1")
  tZone2       Float  @map("t_zone_2")
  tZone3       Float  @map("t_zone_3")
  tZone4       Float  @map("t_zone_4")
  tZone5       Float  @map("t_zone_5")
  tZone6       Float  @map("t_zone_6")
  tZone7       Float  @map("t_zone_7")
  tZone8       Float  @map("t_zone_8")
  tZone9       Float  @map("t_zone_9")
  tZone10      Float  @map("t_zone_10")

  @@map("extrusion")
}

model Formulation {
  id          String                @id @default(uuid())
  code        String                @unique
  name        String
  ownerId     String                @map("owner_id")
  grade       String
  owner       User                  @relation("FormulationOwner", fields: [ownerId], references: [id])
  materials   FormulationMaterial[]
  requests    Request[]             @relation("FormulationRequests")
  testResults TestResult[]

  @@map("formulation")
}

model FormulationMaterial {
  id            String      @id @default(uuid())
  formulationId String      @map("formulation_id")
  materialId    String      @map("material_id")
  percentage    Float
  formulation   Formulation @relation(fields: [formulationId], references: [id])
  material      Material    @relation(fields: [materialId], references: [id])

  @@map("formulation_material")
}

model TestResult {
  id            String      @id @default(uuid())
  formulationId String      @map("formulation_id")
  testName      String      @map("test_name")
  propertyName  String      @map("property_name")
  standard      String?
  condition     String?
  value         Float
  minRange      Float?      @map("min_range")
  maxRange      Float?      @map("max_range")
  formulation   Formulation @relation(fields: [formulationId], references: [id])

  @@map("test_result")
}

model Request {
  id            String        @id @default(uuid())
  requesterId   String        @map("requester_id")
  formulationId String        @map("formulation_id")
  status        RequestStatus
  formulation   Formulation   @relation("FormulationRequests", fields: [formulationId], references: [id])
  requester     User          @relation("RequestRequester", fields: [requesterId], references: [id])

  @@unique([requesterId, formulationId])
  @@map("request")
}

model Specification {
  id        String            @id @default(uuid())
  property  String            @map("property_name")
  label     String
  unit      String?
  type      SpecificationType @default(TEXT)
  createdAt DateTime          @default(now()) @map("created_at")
  updatedAt DateTime          @default(now()) @updatedAt @map("updated_at")

  @@map("specification")
}

enum MaterialFamily {
  POLYMERS
  FILLERS
  ELASTOMERS
  ADDITIVES
  RECYCLE_POLYMERS
}

enum MaterialStatus {
  AVAILABLE
  ARCHIVE
  UNDER_REVIEW
}

enum SpecificationType {
  RANGE
  TEXT
}

model Notification {
  id        String           @id @default(uuid())
  title     String
  message   String
  type      NotificationType
  userId    String           @map("user_id")
  isSeen    Boolean          @default(false) @map("is_seen")
  isRead    Boolean          @default(false) @map("is_read")
  readAt    DateTime?        @map("read_at")
  createdAt DateTime         @default(now()) @map("created_at")
  updatedAt DateTime         @default(now()) @updatedAt @map("updated_at")
  user      User             @relation(fields: [userId], references: [id])

  @@index([userId, isRead, createdAt])
  @@map("notification")
}

model PropertyMetadata {
  id          String         @id @default(uuid())
  propertyKey String         @map("property_key")
  description String
  unit        String?
  type        PropertyType   @default(STRING)
  family      MaterialFamily
  createdAt   DateTime       @default(now()) @map("created_at")
  updatedAt   DateTime       @default(now()) @updatedAt @map("updated_at")

  @@unique([propertyKey, family])
  @@map("property_metadata")
}

enum NotificationType {
  MATERIAL_UPDATE
  REQUEST_UPDATE
  FORMULATION_UPDATE
  SYSTEM_ANNOUNCEMENT
}

enum PropertyType {
  NUMBER
  STRING
}

enum RequestStatus {
  PENDING_APPROVAL
  APPROVED
  REJECTED
}
