import { PrismaClient } from "../generated/prisma";
import {
  seedBasicData,
  seedMaterials,
  seedRecyclePolymerDetails,
  seedFormulationsAndTests,
  seedPropertyMetadata,
} from "./seeds";

const prisma = new PrismaClient();

/**
 * Seed the database with initial data for testing and development.
 */
async function main() {
  const basicData = await seedBasicData(prisma);

  const materials = await seedMaterials(prisma);

  await seedRecyclePolymerDetails(prisma, materials);

  await seedFormulationsAndTests(prisma, basicData.users, materials);

  await seedPropertyMetadata(prisma);
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (error) => {
    console.error(error);
    await prisma.$disconnect();
    throw error;
  });
