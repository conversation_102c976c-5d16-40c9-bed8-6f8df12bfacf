{"prettier.enable": false, "eslint.format.enable": true, "editor.formatOnSave": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint", "[javascript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[javascriptreact]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescriptreact]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "sonarlint.connectedMode.project": {"connectionId": "lmesh", "projectKey": "lmesh_materiact-backend"}}