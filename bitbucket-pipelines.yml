image: node:22

clone:
  depth: full
definitions:
  services:
    docker:
      memory: 2048
  caches:
    sonar: ~/.sonar/cache
  steps:
    - step: &set-variables
        name: Set Global Variables
        script:
          - APP_BASE_NAME=materiact-backend
          - DOCKER_REGISTRY_SERVER_URL=lmesh.azurecr.io
          - echo "CONTAINER_REGISTRY_NAME=lmesh" >> $BITBUCKET_PIPELINES_VARIABLES_PATH
          - echo "APP_BASE_NAME=$APP_BASE_NAME" >> $BITBUCKET_PIPELINES_VARIABLES_PATH
          - echo "AZURE_APP_NAME=$APP_BASE_NAME" >> $BITBUCKET_PIPELINES_VARIABLES_PATH
          - echo "DOCKER_REGISTRY_SERVER_URL=$DOCKER_REGISTRY_SERVER_URL" >> $BITBUCKET_PIPELINES_VARIABLES_PATH
        output-variables:
          - APP_BASE_NAME
          - CONTAINER_REGISTRY_NAME
          - AZURE_APP_NAME
          - DOCKER_REGISTRY_SERVER_URL

    - step: &build-test
        name: Build and Test
        caches:
          - node
        script:
          - HUSKY=0 npm ci
          # - npm run lint
          - npm run build
          - npm test
          - npm run test:e2e
        artifacts:
          - coverage/**

    - step: &sonarcloud
        name: SonarCloud Analysis
        caches:
          - node
          - sonar
        script:
          - HUSKY=0 npm ci
          - npm run test:cov
          - pipe: sonarsource/sonarcloud-scan:4.1.0
          - pipe: sonarsource/sonarcloud-quality-gate:0.2.0

    - step: &build-deploy
        name: Build, Push and Deploy
        services:
          - docker
        caches:
          - docker
        script:
          - |
            # Set environment-specific variables
            export IMAGE_NAME=${DOCKER_REGISTRY_SERVER_URL}/${APP_BASE_NAME}
            case "${BITBUCKET_DEPLOYMENT_ENVIRONMENT}" in
              "production")
                export APP_SUFFIX=""
                export ENVIRONMENT="production"
                ;;
              "staging")
                export APP_SUFFIX="-stg"
                export ENVIRONMENT="staging"
                ;;
              "development")
                export APP_SUFFIX="-dev"
                export ENVIRONMENT="development"
                ;;
              *)
                export APP_SUFFIX="-test"
                export ENVIRONMENT="test"
                ;;
            esac
            
            echo "🚀 Deploying to: $ENVIRONMENT"
            echo "📦 Image: $IMAGE_NAME"
            echo "🏷️  App Suffix: $APP_SUFFIX"
            
          - pipe: atlassian/azure-cli-run:1.1.0
            variables:
              AZURE_APP_ID: ${AZURE_APP_ID}
              AZURE_PASSWORD: ${AZURE_PASSWORD}
              AZURE_TENANT_ID: ${AZURE_TENANT_ID}
              IMAGE_NAME: ${IMAGE_NAME}
              TAG: ${BITBUCKET_BUILD_NUMBER}
              CONTAINER_REGISTRY_NAME: ${CONTAINER_REGISTRY_NAME}
              ENVIRONMENT: ${ENVIRONMENT}
              CLI_COMMAND: "bash ./deploy.sh"
          - pipe: atlassian/azure-web-apps-containers-deploy:1.3.2
            variables:
              AZURE_APP_ID: ${AZURE_APP_ID}
              AZURE_PASSWORD: ${AZURE_PASSWORD}
              AZURE_TENANT_ID: ${AZURE_TENANT_ID}
              AZURE_RESOURCE_GROUP: ${AZURE_RESOURCE_GROUP}
              AZURE_APP_NAME: ${AZURE_APP_NAME}${APP_SUFFIX}
              DOCKER_CUSTOM_IMAGE_NAME: ${IMAGE_NAME}:${BITBUCKET_BUILD_NUMBER}
              DOCKER_REGISTRY_SERVER_URL: ${DOCKER_REGISTRY_SERVER_URL}
              DOCKER_REGISTRY_SERVER_USER: ${DOCKER_REGISTRY_SERVER_USER}
              DOCKER_REGISTRY_SERVER_PASSWORD: ${DOCKER_REGISTRY_SERVER_PASSWORD}

pipelines:
  branches:
    main:
      - step: *set-variables
      - step: *build-test
      - parallel:
          - step: *sonarcloud
          - step:
              <<: *build-deploy
              deployment: development

    production:
      - step: *set-variables
      - step: *build-test
      - step: *sonarcloud
      - step:
          <<: *build-deploy
          deployment: production

    develop:
      - step: *set-variables
      - step: *build-test
      - parallel:
          # - step: *sonarcloud # disabled for prs since we can only analyze "main" branch
          - step:
              <<: *build-deploy
              deployment: development

    staging:
      - step: *set-variables
      - step: *build-test
      - step:
          # - step: *sonarcloud # Disabled for PRs since we can only analyze "main" branch
          <<: *build-deploy
          deployment: staging

  pull-requests:
    "**":
      - step: *set-variables
      - parallel:
          - step: *sonarcloud
          - step:
              <<: *build-test
              script:
                - HUSKY=0 npm ci
                - npm run lint
                - npm run build
                - npm run test:e2e