HOST=localhost
PORT=3001
NODE_ENV="development"
BYPASS_AUTH=false
DATABASE_URL="postgresql://johndoe:randompassword@localhost:5432/mydb?schema=public"

# Azure OAuth2 Configuration
AZURE_REDIRECT_URI=http://localhost:3001/api-docs/oauth2-redirect.html
AZURE_TENANT_ID=YOUR_TENANT_ID
AZURE_CLIENT_ID=YOUR_CLIENT_ID
AZURE_API_SCOPE=api://YOUR_CLIENT_ID/User.Read

# Azure Application Insights
APPLICATIONINSIGHTS_CONNECTION_STRING=

# Azure Communication Service
EMAIL_ENABLED=false 
AZURE_COMMUNICATION_SERVICE_CONNECTION_STRING=
ACS_SENDER_EMAIL=
