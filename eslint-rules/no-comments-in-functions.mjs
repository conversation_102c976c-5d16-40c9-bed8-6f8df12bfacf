export default {
  meta: {
    type: 'problem',
    docs: {
      description: 'disallow comments inside function bodies',
      category: 'Best Practices',
      recommended: false,
    },
    fixable: 'code',
    schema: [],
  },
  create(context) {
    /**
     * Checks if there are any comments inside the function body.
     * @param node node to check
     */
    function checkComments(node) {
      const sourceCode = context.getSourceCode()
      const comments = sourceCode.getCommentsInside(node.body)
      for (const comment of comments) {
        // Skip ESLint directive comments
        if (comment.value.trim().startsWith('eslint')) {
          continue
        }
        if (comment.value.trim().startsWith('Empty')) {
          continue
        }
        context.report({
          node: comment,
          message: 'Comments are not allowed inside function bodies.',
          fix: fixer => fixer.remove(comment),
        })
      }
    }
    return {
      FunctionDeclaration: checkComments,
      FunctionExpression: checkComments,
      ArrowFunctionExpression(node) {
        if (node.body.type === 'BlockStatement') {
          checkComments(node)
        }
      },
    }
  },
}
