# Materiact Backend API

RESTful API for Materiact - A material management and formulation system for engineering teams.

## Tech Stack

- **Framework**: NestJS + Fastify
- **Database**: PostgreSQL + Prisma ORM
- **Authentication**: Azure AD OAuth2
- **Documentation**: Swagger/OpenAPI
- **Testing**: Jest
- **Monitoring**: Azure Monitor + OpenTelemetry + Winston

## Prerequisites

- Node.js 22+ 
- npm/yarn
- PostgreSQL 14+

## Installation

```bash
# Clone repository
git clone <repository-url>
cd materiact-backend

# Ensure you have Node.js 22+ installed
# Check version: node --version

# Install dependencies
npm install

# Setup environment
cp .env.example .env
# Edit .env with your database and Azure AD credentials
```

## Database Setup

```bash
# Generate Prisma client
npx prisma generate

# Push schema to database (first time setup)
npx prisma db push

# Seed database (optional)
npx prisma db seed
```

## Running the App

```bash
# Development
npm run start:dev

# Production
npm run start:prod
```

## API Documentation

- **Swagger UI**: `http://localhost:3000/api-docs`
- **OpenAPI JSON**: `http://localhost:3000/api-docs-json`

## Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## Environment Variables

```env
# Application
HOST=localhost
PORT=3001
NODE_ENV=development
BYPASS_AUTH=false

# Database - Update with your PostgreSQL credentials
DATABASE_URL="****************************************/database_name?schema=public"
SHADOW_DATABASE_URL="postgresql://johndoe:randompassword@localhost:5432/mydb?schema=public"


# Azure OAuth2 Configuration
AZURE_REDIRECT_URI=http://localhost:3001/api-docs/oauth2-redirect.html
AZURE_TENANT_ID=your-tenant-id
AZURE_CLIENT_ID=your-client-id
AZURE_API_SCOPE=api://your-client-id/User.Read

```
