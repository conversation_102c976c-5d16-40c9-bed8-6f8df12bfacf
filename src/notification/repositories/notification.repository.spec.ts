import { NotFoundException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { NotificationRepository } from "./notification.repository";
import { NotificationType } from "@/generated/prisma";
import { PrismaService } from "@/prisma.service";

describe("NotificationRepository", () => {
  let repository: NotificationRepository;

  const mockPrismaService = {
    notification: {
      count: jest.fn(),
      createMany: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
    },
  };

  const mockNotification = {
    createdAt: new Date("2024-01-01T00:00:00Z"),
    id: "notification-1",
    isRead: false,
    isSeen: false,
    message: "This is a test notification",
    readAt: undefined,
    title: "Test Notification",
    type: NotificationType.MATERIAL_UPDATE,
    updatedAt: new Date("2024-01-01T00:00:00Z"),
    userId: "user-1",
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationRepository,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    repository = module.get<NotificationRepository>(NotificationRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(repository).toBeDefined();
  });

  describe("findAll", () => {
    it("should find all notifications with filters", async () => {
      const mockData = [mockNotification];
      mockPrismaService.notification.findMany.mockResolvedValue(mockData);
      mockPrismaService.notification.count.mockResolvedValue(1);

      const filters = {
        userId: "user-1",
        type: NotificationType.MATERIAL_UPDATE,
        isRead: false,
        page: 1,
        limit: 10,
      };

      const result = await repository.findAll(filters);

      expect(mockPrismaService.notification.findMany).toHaveBeenCalledWith({
        where: {
          userId: "user-1",
          type: NotificationType.MATERIAL_UPDATE,
          isRead: false,
        },
        orderBy: { createdAt: "desc" },
        skip: 0,
        take: 10,
      });
      expect(mockPrismaService.notification.count).toHaveBeenCalledWith({
        where: {
          userId: "user-1",
          type: NotificationType.MATERIAL_UPDATE,
          isRead: false,
        },
      });
      expect(result).toEqual({
        data: mockData,
        total: 1,
        page: 1,
        limit: 10,
      });
    });

    it("should handle pagination correctly", async () => {
      mockPrismaService.notification.findMany.mockResolvedValue([]);
      mockPrismaService.notification.count.mockResolvedValue(25);

      const filters = {
        userId: "user-1",
        page: 3,
        limit: 5,
      };

      await repository.findAll(filters);

      expect(mockPrismaService.notification.findMany).toHaveBeenCalledWith({
        where: { userId: "user-1" },
        orderBy: { createdAt: "desc" },
        skip: 10,
        take: 5,
      });
    });

    it("should use default pagination when not provided", async () => {
      mockPrismaService.notification.findMany.mockResolvedValue([]);
      mockPrismaService.notification.count.mockResolvedValue(0);

      const filters = { userId: "user-1" };

      await repository.findAll(filters);

      expect(mockPrismaService.notification.findMany).toHaveBeenCalledWith({
        where: { userId: "user-1" },
        orderBy: { createdAt: "desc" },
        skip: 0,
        take: 10,
      });
    });
  });

  describe("findById", () => {
    it("should find notification by id", async () => {
      mockPrismaService.notification.findUnique.mockResolvedValue(mockNotification);

      const result = await repository.findById("notification-1");

      expect(mockPrismaService.notification.findUnique).toHaveBeenCalledWith({
        where: { id: "notification-1" },
      });
      expect(result).toEqual(mockNotification);
    });

    it("should return null when notification not found", async () => {
      mockPrismaService.notification.findUnique.mockImplementation(() => Promise.resolve());

      const result = await repository.findById("non-existent");

      expect(result).toBeUndefined();
    });
  });

  describe("findOneOrThrow", () => {
    it("should return notification when found", async () => {
      mockPrismaService.notification.findUnique.mockResolvedValue(mockNotification);

      const result = await repository.findOneOrThrow("notification-1");

      expect(result).toEqual(mockNotification);
    });

    it("should throw NotFoundException when notification not found", async () => {
      mockPrismaService.notification.findUnique.mockImplementation(() => Promise.resolve());

      await expect(repository.findOneOrThrow("non-existent")).rejects.toThrow(
        new NotFoundException("Notification with ID 'non-existent' not found")
      );
    });
  });

  describe("markAsRead", () => {
    it("should mark notification as read", async () => {
      const readNotification = { ...mockNotification, isRead: true, readAt: new Date() };
      mockPrismaService.notification.update.mockResolvedValue(readNotification);

      const result = await repository.markAsRead("notification-1");

      expect(mockPrismaService.notification.update).toHaveBeenCalledWith({
        where: { id: "notification-1" },
        data: {
          isRead: true,
          readAt: expect.any(Date),
        },
      });
      expect(result.isRead).toBe(true);
    });
  });

  describe("markAsSeen", () => {
    it("should mark multiple notifications as seen", async () => {
      const notificationIds = ["notification-1", "notification-2"];
      const userId = "user-1";

      await repository.markAsSeen(notificationIds, userId);

      expect(mockPrismaService.notification.updateMany).toHaveBeenCalledWith({
        where: {
          id: { in: notificationIds },
          userId,
          isSeen: false,
        },
        data: {
          isSeen: true,
        },
      });
    });
  });

  describe("markManyAsRead", () => {
    it("should mark multiple notifications as read", async () => {
      const notificationIds = ["notification-1", "notification-2"];
      const userId = "user-1";
      const readNotifications = [
        { ...mockNotification, isRead: true },
        { ...mockNotification, id: "notification-2", isRead: true },
      ];

      mockPrismaService.notification.findMany.mockResolvedValue(readNotifications);

      const result = await repository.markManyAsRead(notificationIds, userId);

      expect(mockPrismaService.notification.updateMany).toHaveBeenCalledWith({
        where: {
          id: { in: notificationIds },
          userId,
          isRead: false,
        },
        data: {
          isRead: true,
          readAt: expect.any(Date),
        },
      });
      expect(mockPrismaService.notification.findMany).toHaveBeenCalledWith({
        where: {
          id: { in: notificationIds },
          userId,
        },
      });
      expect(result).toEqual(readNotifications);
    });
  });

  describe("getUnreadCount", () => {
    it("should return unread notification count for user", async () => {
      mockPrismaService.notification.count.mockResolvedValue(5);

      const result = await repository.getUnreadCount("user-1");

      expect(mockPrismaService.notification.count).toHaveBeenCalledWith({
        where: {
          userId: "user-1",
          isRead: false,
        },
      });
      expect(result).toBe(5);
    });

    it("should return 0 when no unread notifications", async () => {
      mockPrismaService.notification.count.mockResolvedValue(0);

      const result = await repository.getUnreadCount("user-1");

      expect(result).toBe(0);
    });
  });

  describe("createMany", () => {
    it("should create notifications for multiple users", async () => {
      const userIds = ["user-1", "user-2"];
      const title = "New Material Available";
      const message = "A new material has been added to the system";
      const type = NotificationType.MATERIAL_UPDATE;

      const createdNotifications = [
        { ...mockNotification, userId: "user-1", title, message, type },
        { ...mockNotification, id: "notification-2", userId: "user-2", title, message, type },
      ];

      mockPrismaService.notification.findMany.mockResolvedValue(createdNotifications);

      const result = await repository.createMany({ userIds, title, message, type });

      expect(mockPrismaService.notification.createMany).toHaveBeenCalledWith({
        data: [
          { userId: "user-1", title, message, type },
          { userId: "user-2", title, message, type },
        ],
      });
      expect(mockPrismaService.notification.findMany).toHaveBeenCalledWith({
        where: {
          userId: { in: userIds },
          title,
          message,
          type,
        },
        orderBy: { createdAt: "desc" },
        take: 2,
      });
      expect(result).toEqual(createdNotifications);
    });
  });
});
