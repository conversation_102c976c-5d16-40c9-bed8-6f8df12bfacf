import { Injectable, NotFoundException } from "@nestjs/common";
import { PaginationResult } from "@/common/utils";
import { Notification, NotificationType } from "@/generated/prisma";
import { PrismaService } from "@/prisma.service";

export interface NotificationFilters {
  userId: string
  type?: NotificationType
  isRead?: boolean
  page?: number
  limit?: number
}

@Injectable()
export class NotificationRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findAll(filters: NotificationFilters): Promise<PaginationResult<Notification>> {
    const { userId, type, isRead, page = 1, limit = 10 } = filters;
    const skip = (page - 1) * limit;

    const where: any = {
      userId,
    };

    if (type !== undefined) {
      where.type = type;
    }

    if (isRead !== undefined) {
      where.isRead = isRead;
    }

    const [data, total] = await Promise.all([
      this.prisma.notification.findMany({
        where,
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      this.prisma.notification.count({ where }),
    ]);

    return {
      data,
      total,
      page,
      limit,
    };
  }

  async findById(id: string): Promise<Notification | null> {
    return this.prisma.notification.findUnique({
      where: { id },
    });
  }

  async findOneOrThrow(id: string): Promise<Notification> {
    const notification = await this.findById(id);
    if (!notification) {
      throw new NotFoundException(`Notification with ID '${id}' not found`);
    }
    return notification;
  }

  async markAsRead(id: string): Promise<Notification> {
    return this.prisma.notification.update({
      where: { id },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });
  }

  async markAsSeen(ids: string[], userId: string): Promise<void> {
    await this.prisma.notification.updateMany({
      where: {
        id: { in: ids },
        userId,
        isSeen: false,
      },
      data: {
        isSeen: true,
      },
    });
  }

  async markManyAsRead(ids: string[], userId: string): Promise<Notification[]> {
    await this.prisma.notification.updateMany({
      where: {
        id: { in: ids },
        userId,
        isRead: false,
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });
    return this.prisma.notification.findMany({
      where: {
        id: { in: ids },
        userId,
      },
    });
  }

  async getUnreadCount(userId: string): Promise<number> {
    return this.prisma.notification.count({
      where: {
        userId,
        isRead: false,
      },
    });
  }

  async createMany({
    userIds,
    title,
    message,
    type,
  }: {
    userIds: string[]
    title: string
    message: string
    type: NotificationType
  }): Promise<Notification[]> {
    await this.prisma.notification.createMany({
      data: userIds.map(userId => ({
        userId,
        title,
        message,
        type,
      })),
    });
    return this.prisma.notification.findMany({
      where: {
        userId: { in: userIds },
        title,
        message,
        type,
      },
      orderBy: { createdAt: "desc" },
      take: userIds.length,
    });
  }
}
