import {
  <PERSON>,
  Get,
  Query,
  HttpStatus,
  HttpCode,
  UseGuards,
  Req,
  Patch,
  UnauthorizedException,
  Body,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiOAuth2,
  Api<PERSON><PERSON>y,
} from "@nestjs/swagger";
import { NotificationQueryDto, NotificationResponseDto, PaginatedNotificationResponseDto } from "./dto";
import { MarkAsReadDto } from "./dto/mark-as-read.dto";
import { NotificationService } from "./notification.service";
import { AuthGuard } from "@/auth/auth.guard";
import { RoleGuard, Roles } from "@/auth/role.guard";
import { ErrorResponseDto } from "@/common/dto";

@ApiBearerAuth()
@ApiOAuth2([process.env.AZURE_API_SCOPE!])
@UseGuards(AuthGuard, RoleGuard)
@ApiTags("notifications")
@Controller("notifications")
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @Roles()
  @ApiOperation({
    summary: "Get user notifications",
    description: "Retrieve notifications for the authenticated user with optional filtering by type and read status. Response includes unreadCount for all unread notifications. Available to all authenticated users.",
  })
  @ApiQuery({
    name: "page",
    required: false,
    description: "Page number",
    example: 1,
  })
  @ApiQuery({
    name: "limit",
    required: false,
    description: "Number of items per page",
    example: 10,
  })
  @ApiQuery({
    name: "type",
    required: false,
    description: "Filter by notification type",
    enum: ["MATERIAL_UPDATE", "REQUEST_UPDATE", "FORMULATION_UPDATE", "SYSTEM_ANNOUNCEMENT"],
  })
  @ApiQuery({
    name: "isRead",
    required: false,
    description: "Filter by read status",
    type: Boolean,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Notifications retrieved successfully with unread count",
    type: PaginatedNotificationResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: "Unauthorized - Invalid or missing token",
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: "Forbidden - User not authenticated",
    type: ErrorResponseDto,
  })
  async getNotifications(
    @Query() query: NotificationQueryDto,
    @Req() request: any,
  ): Promise<PaginatedNotificationResponseDto> {
    const userId = request.user?.oid || request.headers?.oid;

    if (!userId) {
      throw new UnauthorizedException("User ID not found in request");
    }

    const notifications = await this.notificationService.getNotifications(userId, query);

    const ids = notifications.data.map(n => n.id);
    if (ids.length > 0) {
      await this.notificationService.markAsSeen(ids, userId);

      for (const n of notifications.data) {
        n.isSeen = true;
      }
    }
    return notifications;
  }

  @Patch("mark-as-read")
  @HttpCode(HttpStatus.OK)
  @Roles()
  @ApiOperation({
    summary: "Mark notifications as read",
    description: "Mark multiple notifications as read. Available to all authenticated users.",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Notifications marked as read successfully",
    type: [NotificationResponseDto],
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: "One or more notifications not found",
    type: ErrorResponseDto,
  })
  async markAsRead(
    @Req() request: any,
    @Body() body: MarkAsReadDto,
  ): Promise<NotificationResponseDto[]> {
    const userId = request.user?.oid || request.headers?.oid;
    if (!userId) {
      throw new UnauthorizedException("User ID not found in request");
    }
    return this.notificationService.markManyAsRead(body.ids, userId);
  }
}
