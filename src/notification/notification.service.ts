import { Injectable } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { createPaginatedResponse, normalizePaginationParameters } from "../common/utils";
import { NewFormulationRequestEvent } from "../request/events/new-formulation-request.event";
import { RequestStatusChangedEvent } from "../request/events/request-status-changed.event";
import { NotificationQueryDto, NotificationResponseDto, PaginatedNotificationResponseDto } from "./dto";
import { NotificationRepository } from "./repositories";
import { MaterialStatus, NotificationType } from "@/generated/prisma";
import { MaterialStatusChangedEvent } from "@/material/events";

const messageMap: Record<MaterialStatus, string> = {
  [MaterialStatus.AVAILABLE]: "This material has been characterized and validated by Feedstock & Recycling Engineering team and is available for compare and formulation request. Please check stock on Synergy.",
  [MaterialStatus.ARCHIVE]: "This material is not available for compare and formulation request. It could be a change of strategy/process internally or at the supplier. If you want to have more information, please ask the Feedstock Team",
  [MaterialStatus.UNDER_REVIEW]: "This material has been characterized and will be validated soon by Feedstock & Recycling Engineering team.",
};

const statusDisplayNames: Record<MaterialStatus, string> = {
  [MaterialStatus.AVAILABLE]: "Available",
  [MaterialStatus.ARCHIVE]: "Archived",
  [MaterialStatus.UNDER_REVIEW]: "Under Review",
};
@Injectable()
export class NotificationService {
  public constructor(
    private readonly notificationRepository: NotificationRepository,
  ) {}

  public async createNotification({
    message,
    title,
    type,
    userIds,
  }: {
    userIds: string[]
    title: string
    message: string
    type: NotificationType
  }): Promise<NotificationResponseDto[]> {
    const notifications = await this.notificationRepository.createMany({
      message,
      title,
      type,
      userIds,
    });
    return notifications.map(n => this.mapToNotificationResponse(n));
  }

  @OnEvent("request.new-formulation")
  public async handleNewFormulationNotification(event: NewFormulationRequestEvent) {
    if (!event.managerIds || event.managerIds.length === 0) return;
    await this.createNotification({
      message: event.message,
      title: "New Formulation Request",
      type: NotificationType.REQUEST_UPDATE,
      userIds: event.managerIds,
    });
  }

  @OnEvent("request.status-changed")
  public async handleRequestStatusChangedNotification(event: RequestStatusChangedEvent) {
    if (!event.requesterId) return;
    const statusText = event.newStatus === "APPROVED" ? "approved" : "rejected";
    await this.createNotification({
      message: `Your request for formulation '${event.formulationName}' has been ${statusText}.`,
      title: `Request ${statusText.charAt(0).toUpperCase() + statusText.slice(1)}`,
      type: NotificationType.REQUEST_UPDATE,
      userIds: [event.requesterId],
    });
  }

  @OnEvent("material.status-changed")
  public async handleMaterialStatusChangedNotification(event: MaterialStatusChangedEvent) {
    if (!event.newStatus || !event.userIds || event.userIds.length === 0) return;

    if (!Object.values(MaterialStatus).includes(event.newStatus)) {
      return;
    }

    const status = event.newStatus;

    await this.createNotification({
      message: messageMap[status],
      title: `Material ${statusDisplayNames[status]}`,
      type: NotificationType.MATERIAL_UPDATE,
      userIds: event.userIds,
    });
  }

  public async getNotifications(
    userId: string,
    query: NotificationQueryDto,
  ): Promise<PaginatedNotificationResponseDto> {
    const { page, limit } = normalizePaginationParameters(query.page, query.limit);

    const [result, unreadCount] = await Promise.all([
      this.notificationRepository.findAll({
        isRead: query.isRead,
        limit,
        page,
        type: query.type,
        userId,
      }),
      this.notificationRepository.getUnreadCount(userId),
    ]);

    const mappedData = result.data.map(notification => this.mapToNotificationResponse(notification));

    const paginatedResponse = createPaginatedResponse({
      data: mappedData,
      limit,
      page,
      total: result.total,
    });

    return {
      ...paginatedResponse,
      unreadCount,
    };
  }

  public async markAsRead(id: string): Promise<NotificationResponseDto> {
    await this.notificationRepository.findOneOrThrow(id);
    const updatedNotification = await this.notificationRepository.markAsRead(id);
    return this.mapToNotificationResponse(updatedNotification);
  }

  public async markAsSeen(ids: string[], userId: string): Promise<void> {
    await this.notificationRepository.markAsSeen(ids, userId);
  }

  public async markManyAsRead(ids: string[], userId: string): Promise<NotificationResponseDto[]> {
    const updated = await this.notificationRepository.markManyAsRead(ids, userId);
    return updated.map(n => this.mapToNotificationResponse(n));
  }

  private mapToNotificationResponse(notification: {
    id: string
    title: string
    message: string
    type: NotificationType
    isRead: boolean
    isSeen: boolean
    readAt: Date | null
    createdAt: Date
    updatedAt: Date
  }): NotificationResponseDto {
    return {
      createdAt: notification.createdAt,
      id: notification.id,
      isRead: notification.isRead,
      isSeen: notification.isSeen,
      message: notification.message,
      readAt: notification.readAt,
      title: notification.title,
      type: notification.type,
      updatedAt: notification.updatedAt,
    };
  }
}
