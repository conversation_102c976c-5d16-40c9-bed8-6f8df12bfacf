import { <PERSON>du<PERSON> } from "@nestjs/common";
import { NotificationController } from "./notification.controller";
import { NotificationService } from "./notification.service";
import { NotificationRepository } from "./repositories";
import { AuthModule } from "@/auth/auth.module";
import { PrismaService } from "@/prisma.service";

@Module({
  imports: [AuthModule],
  controllers: [NotificationController],
  providers: [NotificationService, NotificationRepository, PrismaService],
  exports: [NotificationService, NotificationRepository],
})
export class NotificationModule {}
