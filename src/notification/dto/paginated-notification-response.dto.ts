import { ApiProperty } from "@nestjs/swagger";
import { NotificationResponseDto } from "./notification-response.dto";
import { PaginatedResponseDto } from "@/common/dto";

export class PaginatedNotificationResponseDto extends PaginatedResponseDto<NotificationResponseDto> {
  @ApiProperty({ type: [NotificationResponseDto] })
  declare data: NotificationResponseDto[];

  @ApiProperty({ example: 5, description: "Total count of unread notifications for the user" })
  unreadCount: number;
}
