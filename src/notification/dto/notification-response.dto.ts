import { ApiProperty } from "@nestjs/swagger";
import { NotificationType } from "@/generated/prisma";

export class NotificationResponseDto {
  @ApiProperty({ example: "123e4567-e89b-12d3-a456-426614174000" })
  id: string;

  @ApiProperty({ example: "[Material Updates]" })
  title: string;

  @ApiProperty({
    example: "PP-H 1200 has been characterized and validated by Feedstock & Recycling Engineering team and is available for compare and formulation request.",
  })
  message: string;

  @ApiProperty({ enum: NotificationType, example: "MATERIAL_UPDATE" })
  type: NotificationType;

  @ApiProperty({ example: false })
  isRead: boolean;

  @ApiProperty({ example: false })
  isSeen: boolean;

  @ApiProperty({ example: "2024-01-15T10:30:00Z", nullable: true })
  readAt: Date | null;

  @ApiProperty({ example: "2024-01-15T09:00:00Z" })
  createdAt: Date;

  @ApiProperty({ example: "2024-01-15T09:00:00Z" })
  updatedAt: Date;
}
