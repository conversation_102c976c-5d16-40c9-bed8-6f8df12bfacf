import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsOptional, IsEnum, IsBoolean } from "class-validator";
import { PaginationQueryDto } from "@/common/dto";
import { NotificationType } from "@/generated/prisma";

export class NotificationQueryDto extends PaginationQueryDto {
  @ApiProperty({ enum: NotificationType, required: false })
  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @ApiProperty({ example: false, required: false })
  @IsOptional()
  @Transform(({ value }) => value === "true" || value === true)
  @IsBoolean()
  isRead?: boolean;
}
