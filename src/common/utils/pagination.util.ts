import { PaginationMetaDto } from "../dto/pagination-meta.dto";

export interface PaginationResult<T> {
  data: T[]
  total: number
  page: number
  limit: number
}

export interface PaginatedResponse<T> {
  data: T[]
  meta: PaginationMetaDto
}

/**
 * Creates a paginated response with metadata
 * @param result - The pagination result containing data and metadata
 * @returns Formatted paginated response with meta information
 */
export function createPaginatedResponse<T>(
  result: PaginationResult<T>,
): PaginatedResponse<T> {
  const lastPage = Math.ceil(result.total / result.limit);
  const from = result.total > 0 ? (result.page - 1) * result.limit + 1 : 0;
  const to = Math.min(result.page * result.limit, result.total);

  return {
    data: result.data,
    meta: {
      total: result.total,
      perPage: result.limit,
      currentPage: result.page,
      lastPage,
      from,
      to,
    },
  };
}

/**
 * Calculates the database offset for pagination
 * @param page - The current page number
 * @param limit - The number of items per page
 * @returns The calculated offset for database queries
 */
export function calculatePaginationOffset(page: number, limit: number): number {
  return (page - 1) * limit;
}

/**
 * Normalizes pagination parameters with defaults and bounds
 * @param page - The page number (optional)
 * @param limit - The limit per page (optional)
 * @returns Normalized pagination parameters with defaults applied
 */
export function normalizePaginationParameters(
  page?: number,
  limit?: number,
): { page: number, limit: number } {
  const normalizedPage = page && page > 0 ? page : 1;
  const normalizedLimit = limit && limit > 0 && limit <= 100 ? limit : 10;

  return {
    page: normalizedPage,
    limit: normalizedLimit,
  };
}
