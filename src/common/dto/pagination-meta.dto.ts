import { ApiProperty } from "@nestjs/swagger";

export class PaginationMetaDto {
  @ApiProperty({ description: "Total number of items" })
  total: number;

  @ApiProperty({ description: "Number of items per page" })
  perPage: number;

  @ApiProperty({ description: "Current page number" })
  currentPage: number;

  @ApiProperty({ description: "Last page number" })
  lastPage: number;

  @ApiProperty({ description: "Index of first item on current page" })
  from: number;

  @ApiProperty({ description: "Index of last item on current page" })
  to: number;
}
