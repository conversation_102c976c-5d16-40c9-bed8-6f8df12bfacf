import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { PaginationMetaDto } from "./pagination-meta.dto";

export class PaginatedResponseDto<T> {
  @ApiProperty({ description: "The data items for this page" })
  data: T[];

  @ApiProperty({ type: PaginationMetaDto, description: "Pagination metadata" })
  @Type(() => PaginationMetaDto)
  meta: PaginationMetaDto;

  constructor(data: T[], meta: PaginationMetaDto) {
    this.data = data;
    this.meta = meta;
  }
}
