import { NotFoundException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { DepartmentService } from "./department.service";
import { DepartmentRepository } from "./repositories/department.repository";

describe("DepartmentService", () => {
  let service: DepartmentService;
  let repository: DepartmentRepository;

  const mockDepartment = { id: "1", name: "Test Department" };
  const mockDepartments = [mockDepartment];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DepartmentService,
        {
          provide: DepartmentRepository,
          useValue: {
            findAll: jest.fn().mockResolvedValue(mockDepartments),
            findOne: jest.fn().mockImplementation((id: string) => {
              if (id === "1") {
                return Promise.resolve(mockDepartment);
              }
              return Promise.resolve(null);
            }),
          },
        },
      ],
    }).compile();

    service = module.get<DepartmentService>(DepartmentService);
    repository = module.get<DepartmentRepository>(DepartmentRepository);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("findAll", () => {
    it("should return an array of departments", async () => {
      const departments = await service.findAll();
      expect(departments).toEqual([{ id: "1", name: "Test Department" }]);
      expect(repository.findAll).toHaveBeenCalled();
    });
  });

  describe("findOne", () => {
    it("should return a single department if found", async () => {
      const department = await service.findOne("1");
      expect(department).toEqual({ id: "1", name: "Test Department" });
      expect(repository.findOne).toHaveBeenCalledWith("1");
    });

    it("should throw a NotFoundException if the department is not found", async () => {
      await expect(service.findOne("2")).rejects.toThrow(new NotFoundException("Department with ID '2' not found"));
    });
  });

  describe("findOneOrThrow", () => {
    it("should return a single department if found", async () => {
      const department = await service.findOneOrThrow("1");
      expect(department).toEqual({ id: "1", name: "Test Department" });
      expect(repository.findOne).toHaveBeenCalledWith("1");
    });

    it("should throw a NotFoundException if the department is not found", async () => {
      await expect(service.findOneOrThrow("2")).rejects.toThrow(new NotFoundException("Department with ID '2' not found"));
    });
  });
});
