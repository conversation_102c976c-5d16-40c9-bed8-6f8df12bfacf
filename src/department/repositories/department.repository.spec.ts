// Department.repository.spec.ts

import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../../prisma.service";
import { DepartmentRepository } from "./department.repository";

interface MockPrismaDepartmentService {
  department: {
    findMany: jest.Mock
    findUnique: jest.Mock
  }
}

describe("DepartmentRepository", () => {
  let repository: DepartmentRepository;
  let prisma: MockPrismaDepartmentService;

  beforeEach(async () => {
    prisma = {
      department: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DepartmentRepository,
        { provide: PrismaService, useValue: prisma },
      ],
    }).compile();

    repository = module.get<DepartmentRepository>(DepartmentRepository);
  });

  describe("findAll", () => {
    it("should return all departments ordered by name", async () => {
      const mockDepartments = [
        { id: "1", name: "A" },
        { id: "2", name: "B" },
      ];
      prisma.department.findMany.mockResolvedValue(mockDepartments);

      const result = await repository.findAll();

      expect(prisma.department.findMany).toHaveBeenCalledWith({
        orderBy: { name: "asc" },
      });
      expect(result).toEqual(mockDepartments);
    });
  });

  describe("findOne", () => {
    it("should return department by id", async () => {
      const mockDepartment = { id: "1", name: "A" };
      prisma.department.findUnique.mockResolvedValue(mockDepartment);

      const result = await repository.findOne("1");

      expect(prisma.department.findUnique).toHaveBeenCalledWith({
        where: { id: "1" },
      });
      expect(result).toEqual(mockDepartment);
    });
  });
});
