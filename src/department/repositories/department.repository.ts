import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma.service";

@Injectable()
export class DepartmentRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findAll() {
    return this.prisma.department.findMany({
      orderBy: { name: "asc" },
    });
  }

  async findOne(id: string) {
    return this.prisma.department.findUnique({
      where: { id },
    });
  }
}
