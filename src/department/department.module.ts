import { Modu<PERSON> } from "@nestjs/common";
import { PrismaService } from "../prisma.service";
import { DepartmentService } from "./department.service";
import { DepartmentRepository } from "./repositories/department.repository";
import { AuthModule } from "@/auth/auth.module";
import { AuthService } from "@/auth/auth.service";

@Module({
  imports: [AuthModule],
  providers: [DepartmentService, DepartmentRepository, AuthService, PrismaService],
  exports: [DepartmentService, DepartmentRepository],
})
export class DepartmentModule {}
