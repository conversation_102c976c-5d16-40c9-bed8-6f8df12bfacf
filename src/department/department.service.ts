import { Injectable, NotFoundException } from "@nestjs/common";
import { DepartmentResponseDto } from "./dto/department-response.dto";
import { DepartmentRepository } from "./repositories/department.repository";

@Injectable()
export class DepartmentService {
  constructor(private readonly departmentRepository: DepartmentRepository) {}

  async findAll(): Promise<DepartmentResponseDto[]> {
    const departments = await this.departmentRepository.findAll();
    return departments.map(department => this.mapToResponseDto(department));
  }

  async findOne(id: string): Promise<DepartmentResponseDto> {
    const department = await this.departmentRepository.findOne(id);
    if (!department) {
      throw new NotFoundException(`Department with ID '${id}' not found`);
    }
    return this.mapToResponseDto(department);
  }

  async findOneOrThrow(id: string): Promise<DepartmentResponseDto> {
    const department = await this.departmentRepository.findOne(id);

    if (!department) {
      throw new NotFoundException(`Department with ID '${id}' not found`);
    }

    return this.mapToResponseDto(department);
  }

  private mapToResponseDto(department: any): DepartmentResponseDto {
    return {
      id: department.id,
      name: department.name,
    };
  }
}
