import { Controller, Get, Post, Body, Query, HttpStatus, HttpCode, UseGuards, Request as Request_, Param, Delete } from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiOAuth2,
  <PERSON>pi<PERSON><PERSON><PERSON>,
} from "@nestjs/swagger";
import { UserRole } from "../role/role.types";
import { RequestQueryDto, PaginatedRequestResponseDto, CreateRequestDto, RequestResponseDto } from "./dto";
import { RequestActionDto } from "./dto/request-action.dto";
import { RequestService } from "./request.service";
import { AuthGuard } from "@/auth/auth.guard";
import { RoleGuard, Roles } from "@/auth/role.guard";
import { ErrorResponseDto } from "@/common/dto";
import { RequestStatus } from "@/generated/prisma";

@ApiBearerAuth()
@ApiOAuth2([process.env.AZURE_API_SCOPE!])
@UseGuards(AuthGuard, RoleGuard)
@ApiTags("requests")
@Controller("requests")
export class RequestController {
  constructor(private readonly requestService: RequestService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @Roles(
    UserRole.ADMIN,
    UserRole.ENGINEERING_MANAGER,
  )
  @ApiOperation({
    summary: "Get all requests",
    description: "Retrieve all requests with optional filtering by search term and status. Available to ADMIN and Engineering Manager roles only.",
  })
  @ApiQuery({
    name: "search",
    required: false,
    description: "Search across name, email, location, department, and formulation name + grade",
    example: "bumper A",
  })
  @ApiQuery({
    name: "status",
    required: false,
    description: "Filter by request status",
    enum: RequestStatus,
    example: RequestStatus.PENDING_APPROVAL,
  })
  @ApiQuery({
    name: "page",
    required: false,
    description: "Page number",
    example: 1,
  })
  @ApiQuery({
    name: "limit",
    required: false,
    description: "Number of items per page",
    example: 10,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Requests retrieved successfully",
    type: PaginatedRequestResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: "Unauthorized - Invalid or missing token",
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: "Forbidden - User not authorized to access requests",
    type: ErrorResponseDto,
  })
  async findAll(@Query() query: RequestQueryDto): Promise<PaginatedRequestResponseDto> {
    return this.requestService.findAll(query);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @Roles(
    UserRole.ADMIN,
    UserRole.ENGINEERS
  )
  @ApiOperation({
    summary: "Create a new request",
    description: "Create a new request for access to a formulation. Available to all authenticated users.",
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: "Request created successfully",
    type: RequestResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: "Bad request - Invalid input data",
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: "Not found - Formulation not found",
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: "Conflict - Request already exists for this user and formulation",
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: "Unauthorized - Invalid or missing token",
    type: ErrorResponseDto,
  })
  async create(
    @Body() createRequestDto: CreateRequestDto,
    @Request_() request: any
  ): Promise<RequestResponseDto> {
    const userId = request.user?.oid || request.headers?.oid;
    if (!userId) {
      throw new Error("User ID is missing from request context. Make sure authentication is working and user is attached to request.");
    }
    return this.requestService.create(userId, createRequestDto);
  }

  @Post(":id/actions")
  @HttpCode(HttpStatus.OK)
  @Roles(
    UserRole.ADMIN,
    UserRole.ENGINEERING_MANAGER,
  )
  @ApiOperation({
    summary: "Approve or reject a request",
    description: "Approve or reject a request by action. Available to ADMIN and Engineering Manager roles only.",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Request status updated successfully",
    type: RequestResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: "Bad request - Invalid action or status",
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: "Not found - Request not found",
    type: ErrorResponseDto,
  })
  async handleAction(
    @Body() actionDto: RequestActionDto,
    @Request_() request: any,
    @Param("id") id: string,
  ): Promise<RequestResponseDto> {
    const userId = request.user?.oid || request.headers?.oid;
    if (!userId) {
      throw new Error("User ID is missing from request context. Make sure authentication is working and user is attached to request.");
    }
    return this.requestService.handleAction(userId, id, actionDto.action);
  }

  @Delete(":id")
  @HttpCode(HttpStatus.NO_CONTENT)
  @Roles(
    UserRole.ADMIN,
    UserRole.ENGINEERING_MANAGER,
  )
  @ApiOperation({ summary: "Revoke (delete) access request" })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: "Request deleted (access revoked)" })
  async revokeAccess(
    @Param("id") id: string,
    @Request_() request: any
  ): Promise<void> {
    const userId = request.user?.oid || request.headers?.oid;
    if (!userId) {
      throw new Error("User ID is missing from request context.");
    }
    await this.requestService.revokeAccess(userId, id);
  }
}
