import { Injectable } from "@nestjs/common";
import { PrismaService } from "@/prisma.service";

@Injectable()
export class SearchQueryBuilder {
  constructor(private readonly prisma: PrismaService) {}

  async searchRequestWithRelations(search: string): Promise<{ id: string }[]> {
    const searchLower = search.toLowerCase();

    return this.prisma.$queryRaw<{ id: string }[]>`
      SELECT r.id
      FROM request r
      JOIN formulation f ON r.formulation_id = f.id
      JOIN "user" u ON r.requester_id = u.id
      JOIN location l ON u.location_id = l.id
      JOIN department d ON u.department_id = d.id
      WHERE (
        LOWER(u.name) LIKE ${`%${searchLower}%`} OR
        LOWER(u.email) LIKE ${`%${searchLower}%`} OR
        LOWER(l.city) LIKE ${`%${searchLower}%`} OR
        LOWER(l.country) LIKE ${`%${searchLower}%`} OR
        LOWER(d.name) LIKE ${`%${searchLower}%`} OR
        LOWER(CONCAT(f.name, ' ', f.grade)) LIKE ${`%${searchLower}%`}
      )
      ORDER BY r.id DESC
    `;
  }
}
