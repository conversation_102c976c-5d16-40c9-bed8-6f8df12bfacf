import { ApiProperty } from "@nestjs/swagger";
import { RequestStatus } from "@/generated/prisma";

export class LocationDto {
  @ApiProperty({
    description: "Location ID",
    example: "0ac08c3e-b21a-4e41-a671-6865fc0b4b9f",
  })
  id: string;

  @ApiProperty({
    description: "City name",
    example: "Paris",
  })
  city: string;

  @ApiProperty({
    description: "Country name",
    example: "France",
  })
  country: string;
}

export class DepartmentDto {
  @ApiProperty({
    description: "Department ID",
    example: "21477dfd-f663-44a2-9213-510aac3fcdbf",
  })
  id: string;

  @ApiProperty({
    description: "Department name",
    example: "Engineering",
  })
  name: string;
}

export class RequesterDto {
  @ApiProperty({
    description: "User ID",
    example: "48aa4462-0f83-4595-821e-d0dd03671d0f",
  })
  id: string;

  @ApiProperty({
    description: "User name",
    example: "Roger Press",
  })
  name: string;

  @ApiProperty({
    description: "User email",
    example: "<EMAIL>",
  })
  email: string;

  @ApiProperty({
    description: "Location ID",
    example: "0ac08c3e-b21a-4e41-a671-6865fc0b4b9f",
  })
  locationId: string;

  @ApiProperty({
    description: "Department ID",
    example: "21477dfd-f663-44a2-9213-510aac3fcdbf",
  })
  departmentId: string;

  @ApiProperty({
    description: "Role ID",
    example: "cedd7124-a821-4fe2-9b4c-dd7768bc9a6a",
  })
  roleId: string;

  @ApiProperty({
    description: "Creation timestamp",
    example: "2025-07-15T10:34:28.366Z",
  })
  createdAt: string;

  @ApiProperty({
    description: "Last update timestamp",
    example: "2025-07-15T10:34:28.366Z",
  })
  updatedAt: string;

  @ApiProperty({
    description: "User location information",
    type: LocationDto,
  })
  location: LocationDto;

  @ApiProperty({
    description: "User department information",
    type: DepartmentDto,
  })
  department: DepartmentDto;
}

export class FormulationDto {
  @ApiProperty({
    description: "Formulation ID",
    example: "dc8f29e2-e679-4b38-a1a4-41ef59a01a55",
  })
  id: string;

  @ApiProperty({
    description: "Formulation name",
    example: "Bumper",
  })
  name: string;

  @ApiProperty({
    description: "Formulation owner ID",
    example: "a68f285c-35ca-4ac4-901a-d8d80f7d910f",
  })
  ownerId: string;

  @ApiProperty({
    description: "Formulation grade",
    example: "A",
  })
  grade: string;
}

export class RequestResponseDto {
  @ApiProperty({
    description: "Request ID",
    example: "c251f675-3623-4326-8cbc-34609c29641a",
  })
  id: string;

  @ApiProperty({
    enum: RequestStatus,
    description: "Request status",
    example: "REJECTED",
  })
  status: RequestStatus;

  @ApiProperty({
    description: "User who made the request",
    type: RequesterDto,
  })
  requester: RequesterDto;

  @ApiProperty({
    description: "Formulation being requested",
    type: FormulationDto,
  })
  formulation: FormulationDto;
}
