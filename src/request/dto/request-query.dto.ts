import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString, IsEnum } from "class-validator";
import { PaginationQueryDto } from "@/common/dto/pagination-query.dto";
import { RequestStatus } from "@/generated/prisma";

export class RequestQueryDto extends PaginationQueryDto {
  @ApiPropertyOptional({ description: "Search across name, email, location, department, and formulation name + grade" })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: "Filter by request status",
    enum: RequestStatus,
    example: RequestStatus.PENDING_APPROVAL,
  })
  @IsOptional()
  @IsEnum(RequestStatus)
  status?: RequestStatus;
}
