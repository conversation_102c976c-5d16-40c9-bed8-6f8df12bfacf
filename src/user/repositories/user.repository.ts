import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma.service";
import { PaginationResult } from "@/common/utils";

export interface UserData {
  id: string
  name: string
  email: string
  locationId: string
  departmentId: string
  roleId: string
}

export interface UserWithRelations extends UserData {
  location: {
    city: string
    country: string
  }
  department: {
    name: string
  }
  role: {
    code: string
  }
}

export interface CreateUserData {
  name: string
  email: string
  locationId: string
  departmentId: string
  roleId: string
}

export interface UpdateUserData {
  name?: string
  email?: string
  locationId?: string
  departmentId?: string
  roleId?: string
}

export interface UserFilters {
  page?: number
  limit?: number
  locationId?: string
  departmentId?: string
  roleId?: string
  roleCode?: string
  search?: string
}

@Injectable()
export class UserRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(userData: CreateUserData): Promise<UserWithRelations> {
    return this.prisma.user.create({
      data: userData,
      include: {
        role: true,
        location: true,
        department: true,
      },
    });
  }

  async findAll(filters: UserFilters = {}): Promise<PaginationResult<UserWithRelations>> {
    const where: any = {};
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const skip = (page - 1) * limit;

    if (filters.locationId) {
      where.locationId = filters.locationId;
    }

    if (filters.departmentId) {
      where.departmentId = filters.departmentId;
    }

    if (filters.roleId) {
      where.roleId = filters.roleId;
    }

    if (filters.roleCode) {
      where.role = {
        code: filters.roleCode,
      };
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: "insensitive" } },
        { email: { contains: filters.search, mode: "insensitive" } },
      ];
    }

    const [data, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        include: {
          role: true,
          location: true,
          department: true,
        },
        orderBy: { name: "asc" },
        skip,
        take: limit,
      }),
      this.prisma.user.count({ where }),
    ]);

    return {
      data,
      total,
      page,
      limit,
    };
  }

  async findById(id: string): Promise<UserWithRelations | null> {
    return this.prisma.user.findUnique({
      where: { id },
      include: {
        role: true,
        location: true,
        department: true,
      },
    });
  }

  async findOneOrThrow(id: string): Promise<UserWithRelations> {
    const user = await this.findById(id);
    if (!user) {
      throw new Error(`User with ID '${id}' not found`);
    }
    return user;
  }

  async findByEmail(email: string): Promise<UserData | null> {
    return this.prisma.user.findUnique({
      where: { email },
    });
  }

  async update(id: string, userData: UpdateUserData): Promise<UserWithRelations> {
    return this.prisma.user.update({
      where: { id },
      data: userData,
      include: {
        role: true,
        location: true,
        department: true,
      },
    });
  }

  async delete(id: string): Promise<void> {
    await this.prisma.user.delete({
      where: { id },
    });
  }
}
