import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../../prisma.service";
import { UserRole } from "../../role/role.types";
import { CreateUserData, UpdateUserData, UserFilters, UserRepository } from "./user.repository";

describe("UserRepository", () => {
  let repository: UserRepository;
  let prismaService: PrismaService;

  const mockUser = {
    id: "user-123",
    name: "<PERSON>",
    email: "<EMAIL>",
    locationId: "loc-123",
    departmentId: "dept-456",
    roleId: "role-789",
    location: {
      city: "Paris",
      country: "France",
    },
    department: {
      name: "Technology",
    },
    role: {
      name: "Engineers",
    },
  };

  const mockPrismaUser = {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  };

  beforeEach(async () => {
    const mockPrismaService = {
      user: mockPrismaUser,
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserRepository,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    repository = module.get<UserRepository>(UserRepository);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create a new user", async () => {
      const createUserData: CreateUserData = {
        name: "John Doe",
        email: "<EMAIL>",
        locationId: "loc-123",
        departmentId: "dept-456",
        roleId: "role-789",
      };

      mockPrismaUser.create.mockResolvedValue(mockUser);

      const result = await repository.create(createUserData);

      expect(prismaService.user.create).toHaveBeenCalledWith({
        data: createUserData,
        include: {
          role: true,
          location: true,
          department: true,
        },
      });
      expect(result).toEqual(mockUser);
    });
  });

  describe("findAll", () => {
    it("should return paginated users with default parameters", async () => {
      const mockUsers = [mockUser];
      const filters: UserFilters = {};

      mockPrismaUser.findMany.mockResolvedValue(mockUsers);
      mockPrismaUser.count.mockResolvedValue(1);

      const result = await repository.findAll(filters);

      expect(prismaService.user.findMany).toHaveBeenCalledWith({
        where: {},
        include: {
          role: true,
          location: true,
          department: true,
        },
        orderBy: { name: "asc" },
        skip: 0,
        take: 10,
      });
      expect(prismaService.user.count).toHaveBeenCalledWith({ where: {} });
      expect(result).toEqual({
        data: mockUsers,
        total: 1,
        page: 1,
        limit: 10,
      });
    });

    it("should apply location filter", async () => {
      const filters: UserFilters = {
        locationId: "loc-123",
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(prismaService.user.findMany).toHaveBeenCalledWith({
        where: { locationId: "loc-123" },
        include: {
          role: true,
          location: true,
          department: true,
        },
        orderBy: { name: "asc" },
        skip: 0,
        take: 10,
      });
    });

    it("should apply department filter", async () => {
      const filters: UserFilters = {
        departmentId: "dept-456",
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(prismaService.user.findMany).toHaveBeenCalledWith({
        where: { departmentId: "dept-456" },
        include: {
          role: true,
          location: true,
          department: true,
        },
        orderBy: { name: "asc" },
        skip: 0,
        take: 10,
      });
    });

    it("should apply role filter", async () => {
      const filters: UserFilters = {
        roleId: "role-789",
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(prismaService.user.findMany).toHaveBeenCalledWith({
        where: { roleId: "role-789" },
        include: {
          role: true,
          location: true,
          department: true,
        },
        orderBy: { name: "asc" },
        skip: 0,
        take: 10,
      });
    });

    it("should apply roleCode filter", async () => {
      const filters: UserFilters = {
        roleCode: UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(prismaService.user.findMany).toHaveBeenCalledWith({
        include: {
          department: true,
          location: true,
          role: true,
        },
        orderBy: { name: "asc" },
        skip: 0,
        take: 10,
        where: {
          role: {
            code: UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
          },
        },
      });
    });

    it("should apply search filter", async () => {
      const filters: UserFilters = {
        search: "john",
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(prismaService.user.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { name: { contains: "john", mode: "insensitive" } },
            { email: { contains: "john", mode: "insensitive" } },
          ],
        },
        include: {
          role: true,
          location: true,
          department: true,
        },
        orderBy: { name: "asc" },
        skip: 0,
        take: 10,
      });
    });

    it("should apply pagination", async () => {
      const filters: UserFilters = {
        page: 2,
        limit: 5,
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(prismaService.user.findMany).toHaveBeenCalledWith({
        where: {},
        include: {
          role: true,
          location: true,
          department: true,
        },
        orderBy: { name: "asc" },
        skip: 5,
        take: 5,
      });
    });

    it("should apply multiple filters", async () => {
      const filters: UserFilters = {
        locationId: "loc-123",
        departmentId: "dept-456",
        roleId: "role-789",
        search: "john",
        page: 2,
        limit: 5,
      };

      mockPrismaUser.findMany.mockResolvedValue([]);
      mockPrismaUser.count.mockResolvedValue(0);

      await repository.findAll(filters);

      expect(prismaService.user.findMany).toHaveBeenCalledWith({
        where: {
          locationId: "loc-123",
          departmentId: "dept-456",
          roleId: "role-789",
          OR: [
            { name: { contains: "john", mode: "insensitive" } },
            { email: { contains: "john", mode: "insensitive" } },
          ],
        },
        include: {
          role: true,
          location: true,
          department: true,
        },
        orderBy: { name: "asc" },
        skip: 5,
        take: 5,
      });
    });
  });

  describe("findById", () => {
    it("should return user by ID", async () => {
      mockPrismaUser.findUnique.mockResolvedValue(mockUser);

      const result = await repository.findById("user-123");

      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: "user-123" },
        include: {
          role: true,
          location: true,
          department: true,
        },
      });
      expect(result).toEqual(mockUser);
    });

    it("should return null when user not found", async () => {
      mockPrismaUser.findUnique.mockResolvedValue(null);

      const result = await repository.findById("non-existent");

      expect(result).toBeNull();
    });
  });

  describe("findOneOrThrow", () => {
    it("should return user when found", async () => {
      mockPrismaUser.findUnique.mockResolvedValue(mockUser);

      const result = await repository.findOneOrThrow("user-123");

      expect(result).toEqual(mockUser);
    });

    it("should throw error when user not found", async () => {
      mockPrismaUser.findUnique.mockResolvedValue(null);

      await expect(repository.findOneOrThrow("non-existent")).rejects.toThrow(
        "User with ID 'non-existent' not found",
      );
    });
  });

  describe("findByEmail", () => {
    it("should return user by email", async () => {
      const userWithoutRelations = {
        id: "user-123",
        name: "John Doe",
        email: "<EMAIL>",
        locationId: "loc-123",
        departmentId: "dept-456",
        roleId: "role-789",
      };

      mockPrismaUser.findUnique.mockResolvedValue(userWithoutRelations);

      const result = await repository.findByEmail("<EMAIL>");

      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { email: "<EMAIL>" },
      });
      expect(result).toEqual(userWithoutRelations);
    });

    it("should return null when user not found", async () => {
      mockPrismaUser.findUnique.mockResolvedValue(null);

      const result = await repository.findByEmail("<EMAIL>");

      expect(result).toBeNull();
    });
  });

  describe("update", () => {
    it("should update user", async () => {
      const updateData: UpdateUserData = {
        name: "John Updated",
        email: "<EMAIL>",
      };

      const updatedUser = { ...mockUser, ...updateData };
      mockPrismaUser.update.mockResolvedValue(updatedUser);

      const result = await repository.update("user-123", updateData);

      expect(prismaService.user.update).toHaveBeenCalledWith({
        where: { id: "user-123" },
        data: updateData,
        include: {
          role: true,
          location: true,
          department: true,
        },
      });
      expect(result).toEqual(updatedUser);
    });
  });

  describe("delete", () => {
    it("should delete user", async () => {
      mockPrismaUser.delete.mockResolvedValue(mockUser);

      await repository.delete("user-123");

      expect(prismaService.user.delete).toHaveBeenCalledWith({
        where: { id: "user-123" },
      });
    });
  });
});
