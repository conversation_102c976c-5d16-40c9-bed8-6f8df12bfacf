import { NotFoundException, ConflictException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import {
  UserResponseDto,
  CreateUserDto,
  UpdateUserDto,
  UserFilterDto,
  PaginatedUserResponseDto,
} from "./dto";
import { UserController } from "./user.controller";
import { UserService } from "./user.service";
import { AuthGuard } from "@/auth/auth.guard";
import { RoleGuard } from "@/auth/role.guard";

describe("UserController", () => {
  let controller: UserController;
  let userService: UserService;

  beforeEach(async () => {
    const mockUserService = {
      findOne: jest.fn(),
      create: jest.fn(),
      findAll: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
      getFilterOptions: jest.fn(),
    };

    const mockAuthGuard = {
      canActivate: jest.fn().mockReturnValue(true),
    };

    const mockRoleGuard = {
      canActivate: jest.fn().mockReturnValue(true),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        {
          provide: UserService,
          useValue: mockUserService,
        },
      ],
    })
      .overrideGuard(AuthGuard)
      .useValue(mockAuthGuard)
      .overrideGuard(RoleGuard)
      .useValue(mockRoleGuard)
      .compile();

    controller = module.get<UserController>(UserController);
    userService = module.get<UserService>(UserService);
  });

  describe("getCurrentUserProfile", () => {
    it("should return user profile when user exists", async () => {
      const userId = "test-user-id";
      const mockRequest = {
        user: {
          oid: userId,
          role: "Engineers",
        },
      };

      const expectedUser: UserResponseDto = {
        id: userId,
        name: "John Doe",
        email: "<EMAIL>",
        location: "Paris, France",
        department: "Technology",
        role: "Engineers",
      };

      userService.findOne = jest.fn().mockResolvedValue(expectedUser);

      const result = await controller.getCurrentUserProfile(mockRequest);

      expect(userService.findOne).toHaveBeenCalledWith(userId);
      expect(userService.findOne).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedUser);
    });

    it("should throw NotFoundException when user does not exist", async () => {
      const userId = "non-existent-user-id";
      const mockRequest = {
        user: {
          oid: userId,
          role: "Engineers",
        },
      };

      userService.findOne = jest.fn().mockRejectedValue(new NotFoundException("User not found"));

      await expect(controller.getCurrentUserProfile(mockRequest)).rejects.toThrow(
        NotFoundException,
      );
      expect(userService.findOne).toHaveBeenCalledWith(userId);
      expect(userService.findOne).toHaveBeenCalledTimes(1);
    });

    it("should extract user ID from request.user.oid correctly", async () => {
      const userId = "specific-test-id";
      const mockRequest = {
        user: {
          oid: userId,
          role: "Admin",
          upn: "<EMAIL>",
        },
      };

      const mockUser: UserResponseDto = {
        id: userId,
        name: "Test User",
        email: "<EMAIL>",
        location: "Paris, France",
        department: "Technology",
        role: "Admin",
      };

      userService.findOne = jest.fn().mockResolvedValue(mockUser);

      const result = await controller.getCurrentUserProfile(mockRequest);

      expect(userService.findOne).toHaveBeenCalledWith(userId);
      expect(result).toEqual(mockUser);
    });
  });

  describe("create", () => {
    it("should create a new user successfully", async () => {
      const createUserDto: CreateUserDto = {
        name: "John Doe",
        email: "<EMAIL>",
        locationId: "loc-123",
        departmentId: "dept-456",
        roleId: "role-789",
      };

      const expectedUser: UserResponseDto = {
        id: "user-123",
        name: "John Doe",
        email: "<EMAIL>",
        location: "Paris, France",
        department: "Technology",
        role: "Engineers",
      };

      userService.create = jest.fn().mockResolvedValue(expectedUser);

      const result = await controller.create(createUserDto);

      expect(userService.create).toHaveBeenCalledWith(createUserDto);
      expect(userService.create).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedUser);
    });

    it("should throw ConflictException when email already exists", async () => {
      const createUserDto: CreateUserDto = {
        name: "John Doe",
        email: "<EMAIL>",
        locationId: "loc-123",
        departmentId: "dept-456",
        roleId: "role-789",
      };

      userService.create = jest.fn().mockRejectedValue(
        new ConflictException("User with this email already exists"),
      );

      await expect(controller.create(createUserDto)).rejects.toThrow(ConflictException);
      expect(userService.create).toHaveBeenCalledWith(createUserDto);
    });
  });

  describe("findAll", () => {
    it("should return paginated list of users", async () => {
      const filters: UserFilterDto = {
        page: 1,
        limit: 10,
        search: "john",
      };

      const expectedResponse: PaginatedUserResponseDto = {
        data: [
          {
            id: "user-1",
            name: "John Doe",
            email: "<EMAIL>",
            location: "Paris, France",
            department: "Technology",
            role: "Engineers",
          },
        ],
        meta: {
          total: 1,
          perPage: 10,
          currentPage: 1,
          lastPage: 1,
          from: 1,
          to: 1,
        },
      };

      userService.findAll = jest.fn().mockResolvedValue(expectedResponse);

      const result = await controller.findAll(filters);

      expect(userService.findAll).toHaveBeenCalledWith(filters);
      expect(userService.findAll).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResponse);
    });

    it("should return users with no filters", async () => {
      const filters: UserFilterDto = {};

      const expectedResponse: PaginatedUserResponseDto = {
        data: [],
        meta: {
          total: 0,
          perPage: 10,
          currentPage: 1,
          lastPage: 0,
          from: 0,
          to: 0,
        },
      };

      userService.findAll = jest.fn().mockResolvedValue(expectedResponse);

      const result = await controller.findAll(filters);

      expect(userService.findAll).toHaveBeenCalledWith(filters);
      expect(result).toEqual(expectedResponse);
    });
  });

  describe("getFilterOptions", () => {
    it("should return filter options for dropdowns", async () => {
      const expectedOptions = {
        roles: [{ id: "role-1", name: "Engineers" }],
        locations: [{ id: "loc-1", name: "Paris, France" }],
        departments: [{ id: "dept-1", name: "Technology" }],
      };

      userService.getFilterOptions = jest.fn().mockResolvedValue(expectedOptions);

      const result = await controller.getFilterOptions();

      expect(userService.getFilterOptions).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedOptions);
    });
  });

  describe("findOne", () => {
    it("should return user by ID", async () => {
      const userId = "user-123";
      const expectedUser: UserResponseDto = {
        id: userId,
        name: "John Doe",
        email: "<EMAIL>",
        location: "Paris, France",
        department: "Technology",
        role: "Engineers",
      };

      userService.findOne = jest.fn().mockResolvedValue(expectedUser);

      const result = await controller.findOne(userId);

      expect(userService.findOne).toHaveBeenCalledWith(userId);
      expect(userService.findOne).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedUser);
    });

    it("should throw NotFoundException when user not found", async () => {
      const userId = "non-existent-id";

      userService.findOne = jest.fn().mockRejectedValue(new NotFoundException("User not found"));

      await expect(controller.findOne(userId)).rejects.toThrow(NotFoundException);
      expect(userService.findOne).toHaveBeenCalledWith(userId);
    });
  });

  describe("update", () => {
    it("should update user successfully", async () => {
      const userId = "user-123";
      const updateUserDto: UpdateUserDto = {
        name: "John Updated",
        email: "<EMAIL>",
      };

      const expectedUser: UserResponseDto = {
        id: userId,
        name: "John Updated",
        email: "<EMAIL>",
        location: "Paris, France",
        department: "Technology",
        role: "Engineers",
      };

      userService.update = jest.fn().mockResolvedValue(expectedUser);

      const result = await controller.update(userId, updateUserDto);

      expect(userService.update).toHaveBeenCalledWith(userId, updateUserDto);
      expect(userService.update).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedUser);
    });

    it("should throw NotFoundException when user not found", async () => {
      const userId = "non-existent-id";
      const updateUserDto: UpdateUserDto = {
        name: "Updated Name",
      };

      userService.update = jest.fn().mockRejectedValue(new NotFoundException("User not found"));

      await expect(controller.update(userId, updateUserDto)).rejects.toThrow(NotFoundException);
      expect(userService.update).toHaveBeenCalledWith(userId, updateUserDto);
    });

    it("should throw ConflictException when email already exists", async () => {
      const userId = "user-123";
      const updateUserDto: UpdateUserDto = {
        email: "<EMAIL>",
      };

      userService.update = jest.fn().mockRejectedValue(
        new ConflictException("User with this email already exists"),
      );

      await expect(controller.update(userId, updateUserDto)).rejects.toThrow(ConflictException);
      expect(userService.update).toHaveBeenCalledWith(userId, updateUserDto);
    });
  });

  describe("remove", () => {
    it("should delete user successfully", async () => {
      const userId = "user-123";

      userService.remove = jest.fn();

      await controller.remove(userId);

      expect(userService.remove).toHaveBeenCalledWith(userId);
      expect(userService.remove).toHaveBeenCalledTimes(1);
    });

    it("should throw NotFoundException when user not found", async () => {
      const userId = "non-existent-id";

      userService.remove = jest.fn().mockRejectedValue(new NotFoundException("User not found"));

      await expect(controller.remove(userId)).rejects.toThrow(NotFoundException);
      expect(userService.remove).toHaveBeenCalledWith(userId);
    });
  });
});
