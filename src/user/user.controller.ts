import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpStatus,
  UseGuards,
  Req,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiOAuth2,
} from "@nestjs/swagger";
import { ErrorResponseDto } from "../common/dto";
import {
  CreateUserDto,
  UpdateUserDto,
  UserResponseDto,
  UserFilterDto,
  PaginatedUserResponseDto,
} from "./dto";
import { UserService } from "./user.service";
import { AuthGuard } from "@/auth/auth.guard";
import { RoleGuard, Roles } from "@/auth/role.guard";
import { UserRole } from "@/role/role.types";

@ApiBearerAuth()
@ApiOAuth2([process.env.AZURE_API_SCOPE!])
@UseGuards(AuthGuard, RoleGuard)
@ApiTags("users")
@Controller("users")
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Roles(UserRole.ADMIN)
  @Post()
  @ApiOperation({ summary: "Create a new user" })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: "User created successfully",
    type: UserResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: "Invalid input data",
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: "User with email already exists",
    type: ErrorResponseDto,
  })
  async create(@Body() createUserDto: CreateUserDto): Promise<UserResponseDto> {
    return this.userService.create(createUserDto);
  }

  @Roles(UserRole.ADMIN)
  @Get()
  @ApiOperation({ summary: "Get paginated list of users with filters" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Users retrieved successfully",
    type: PaginatedUserResponseDto,
  })
  @ApiQuery({
    name: "page",
    required: false,
    description: "Page number",
    example: 1,
  })
  @ApiQuery({
    name: "limit",
    required: false,
    description: "Number of items per page",
    example: 10,
  })
  @ApiQuery({
    name: "search",
    required: false,
    description: "Search by name or email",
  })
  @ApiQuery({
    name: "roleId",
    required: false,
    description: "Filter by role ID",
  })
  @ApiQuery({
    name: "departmentId",
    required: false,
    description: "Filter by department ID",
  })
  @ApiQuery({
    name: "locationId",
    required: false,
    description: "Filter by location ID",
  })
  async findAll(@Query() filters: UserFilterDto): Promise<PaginatedUserResponseDto> {
    return this.userService.findAll(filters);
  }

  @Roles(UserRole.ADMIN)
  @Get("filters")
  @ApiOperation({ summary: "Get filter options for user dropdowns" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Filter options retrieved successfully",
  })
  async getFilterOptions() {
    return this.userService.getFilterOptions();
  }

  @Get("profile")
  @ApiOperation({ summary: "Get current user profile" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "User profile retrieved successfully",
    type: UserResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: "User not found",
    type: ErrorResponseDto,
  })
  async getCurrentUserProfile(@Req() request: any): Promise<UserResponseDto> {
    const userId = request.user.oid;
    return this.userService.findOne(userId);
  }

  @Roles(UserRole.ADMIN)
  @Get(":id")
  @ApiOperation({ summary: "Get user by ID" })
  @ApiParam({
    name: "id",
    description: "User ID",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "User retrieved successfully",
    type: UserResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: "User not found",
    type: ErrorResponseDto,
  })
  async findOne(@Param("id") id: string): Promise<UserResponseDto> {
    return this.userService.findOne(id);
  }

  @Roles(UserRole.ADMIN)
  @Patch(":id")
  @ApiOperation({ summary: "Update user by ID" })
  @ApiParam({
    name: "id",
    description: "User ID",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "User updated successfully",
    type: UserResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: "User not found",
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: "Invalid input data",
    type: ErrorResponseDto,
  })
  async update(
    @Param("id") id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<UserResponseDto> {
    return this.userService.update(id, updateUserDto);
  }

  @Roles(UserRole.ADMIN)
  @Delete(":id")
  @ApiOperation({ summary: "Delete user by ID" })
  @ApiParam({
    name: "id",
    description: "User ID",
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: "User deleted successfully",
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: "User not found",
    type: ErrorResponseDto,
  })
  async remove(@Param("id") id: string): Promise<void> {
    return this.userService.remove(id);
  }
}
