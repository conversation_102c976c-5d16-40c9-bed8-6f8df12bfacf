import { <PERSON>du<PERSON> } from "@nestjs/common";
import { DepartmentModule } from "../department/department.module";
import { LocationModule } from "../location/location.module";
import { PrismaService } from "../prisma.service";
import { RoleModule } from "../role/role.module";
import { UserRepository } from "./repositories/user.repository";
import { UserController } from "./user.controller";
import { UserService } from "./user.service";
import { AuthModule } from "@/auth/auth.module";

@Module({
  imports: [AuthModule, RoleModule, LocationModule, DepartmentModule],
  controllers: [UserController],
  providers: [UserService, UserRepository, PrismaService],
  exports: [UserService, UserRepository],
})
export class UserModule {}
