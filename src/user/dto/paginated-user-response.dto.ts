import { ApiProperty } from "@nestjs/swagger";
import { PaginationMetaDto } from "../../common/dto/pagination-meta.dto";
import { UserResponseDto } from "./user-response.dto";

export class PaginatedUserResponseDto {
  @ApiProperty({ description: "Array of users", type: [UserResponseDto] })
  data: UserResponseDto[];

  @ApiProperty({ description: "Pagination metadata", type: PaginationMetaDto })
  meta: PaginationMetaDto;
}
