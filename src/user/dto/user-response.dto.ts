import { ApiProperty } from "@nestjs/swagger";

export class UserResponseDto {
  @ApiProperty({ example: "550e8400-e29b-41d4-a716-446655440000" })
  id: string;

  @ApiProperty({ example: "Omar Press" })
  name: string;

  @ApiProperty({ example: "<EMAIL>" })
  email: string;

  @ApiProperty({ example: "Paris, France" })
  location: string;

  @ApiProperty({ example: "Technology" })
  department: string;

  @ApiProperty({ example: "ADMIN" })
  role: string;
}
