import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsString, IsUUID } from "class-validator";

export class CreateUserDto {
  @ApiProperty({ example: "Omar Press" })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ example: "<EMAIL>" })
  @IsEmail()
  email: string;

  @ApiProperty({ example: "9c5ae51c-7ca3-4fa6-a8e3-c8df27782ae6", description: "Location ID" })
  @IsUUID()
  locationId: string;

  @ApiProperty({ example: "921f82e3-e487-4d07-a585-6e86fb988fdc", description: "Department ID" })
  @IsUUID()
  departmentId: string;

  @ApiProperty({ example: "51c4f5ec-72f5-45b4-bec2-7e4be6a7b548", description: "Role ID" })
  @IsUUID()
  roleId: string;
}
