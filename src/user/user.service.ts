import { ConflictException, Injectable, NotFoundException } from "@nestjs/common";
import { createPaginatedResponse, normalizePaginationParameters } from "../common/utils";
import { CreateUserDto, PaginatedUserResponseDto, UpdateUserDto, UserFilterDto, UserResponseDto } from "./dto";
import { UserRepository, UserWithRelations } from "./repositories";
import { DepartmentService } from "@/department";
import { LocationService } from "@/location";
import { RoleService } from "@/role";

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly roleService: RoleService,
    private readonly locationService: LocationService,
    private readonly departmentService: DepartmentService,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<UserResponseDto> {
    const existingUser = await this.userRepository.findByEmail(createUserDto.email);

    if (existingUser) {
      throw new ConflictException("User with this email already exists");
    }

    await Promise.all([
      this.locationService.findOneOrThrow(createUserDto.locationId),
      this.departmentService.findOneOrThrow(createUserDto.departmentId),
      this.roleService.findOneOrThrow(createUserDto.roleId),
    ]);

    const user = await this.userRepository.create({
      name: createUserDto.name,
      email: createUserDto.email,
      locationId: createUserDto.locationId,
      departmentId: createUserDto.departmentId,
      roleId: createUserDto.roleId,
    });

    return this.mapToUserResponse(user);
  }

  async findAll(filters: UserFilterDto): Promise<PaginatedUserResponseDto> {
    const { page, limit } = normalizePaginationParameters(filters.page, filters.limit);

    const result = await this.userRepository.findAll({
      locationId: filters.locationId,
      departmentId: filters.departmentId,
      roleId: filters.roleId,
      roleCode: filters.roleCode,
      search: filters.search,
      page,
      limit,
    });

    const mappedData = result.data.map(user => this.mapToUserResponse(user));

    return createPaginatedResponse({
      data: mappedData,
      total: result.total,
      page: result.page,
      limit: result.limit,
    });
  }

  async findOne(id: string): Promise<UserResponseDto> {
    const user = await this.userRepository.findOneOrThrow(id);
    return this.mapToUserResponse(user);
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<UserResponseDto> {
    const existingUser = await this.userRepository.findOneOrThrow(id);
    if (updateUserDto.email && updateUserDto.email !== existingUser.email) {
      const emailExists = await this.userRepository.findByEmail(updateUserDto.email);

      if (emailExists) {
        throw new ConflictException("User with this email already exists");
      }
    }

    const updateData: any = {
      name: updateUserDto.name,
      email: updateUserDto.email,
    };

    if (updateUserDto.locationId) {
      const location = await this.locationService.findOne(updateUserDto.locationId);
      if (!location) {
        throw new NotFoundException(`Location with ID '${updateUserDto.locationId}' not found`);
      }
      updateData.locationId = updateUserDto.locationId;
    }

    if (updateUserDto.departmentId) {
      const department = await this.departmentService.findOne(updateUserDto.departmentId);
      if (!department) {
        throw new NotFoundException(`Department with ID '${updateUserDto.departmentId}' not found`);
      }
      updateData.departmentId = updateUserDto.departmentId;
    }

    if (updateUserDto.roleId) {
      const role = await this.roleService.findOne(updateUserDto.roleId);
      if (!role) {
        throw new NotFoundException(`Role with ID '${updateUserDto.roleId}' not found`);
      }
      updateData.roleId = updateUserDto.roleId;
    }

    const user = await this.userRepository.update(id, updateData);

    return this.mapToUserResponse(user);
  }

  async remove(id: string): Promise<void> {
    const user = await this.userRepository.findById(id);

    if (!user) {
      throw new NotFoundException(`User with ID '${id}' not found`);
    }
    await this.userRepository.delete(id);
  }

  async getFilterOptions() {
    const [roles, locations, departments] = await Promise.all([
      this.roleService.findAll(),
      this.locationService.findAll(),
      this.departmentService.findAll(),
    ]);

    return {
      roles: roles.map(role => ({ id: role.id, name: role.name })),
      locations: locations.map(location => ({
        id: location.id,
        name: `${location.city}, ${location.country}`,
      })),
      departments: departments.map(department => ({
        id: department.id,
        name: department.name,
      })),
    };
  }

  private mapToUserResponse(user: UserWithRelations): UserResponseDto {
    return {
      id: user.id,
      name: user.name,
      email: user.email,
      location: `${user.location.city}, ${user.location.country}`,
      department: user.department.name,
      role: user.role.code,
    };
  }
}
