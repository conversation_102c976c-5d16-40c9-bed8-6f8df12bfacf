import { Injectable, OnM<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/common";
import { trace, context, SpanStatusCode } from "@opentelemetry/api";
import { PrismaClient } from "@/generated/prisma";

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleDestroy {
  private readonly tracer = trace.getTracer("prisma");

  constructor() {
    super();

    return this.$extends({
      query: {
        $allOperations: async ({ model, operation, args, query }) => {
          const span = this.tracer.startSpan(`prisma:${model}.${operation}`, {
            attributes: {
              "db.operation": operation,
              "db.collection.name": model,
              "prisma.model": model,
              "prisma.action": operation,
            },
          });

          return context.with(trace.setSpan(context.active(), span), async () => {
            try {
              const before = Date.now();
              const result = await query(args);
              const after = Date.now();

              span.setAttributes({
                "db.duration": after - before,
                "prisma.result_count": Array.isArray(result) ? result.length : (result ? 1 : 0),
              });

              span.setStatus({ code: SpanStatusCode.OK });
              return result;
            }
            catch (error) {
              span.recordException(error as Error);
              span.setStatus({
                code: SpanStatusCode.ERROR,
                message: (error as Error).message,
              });
              throw error;
            }
            finally {
              span.end();
            }
          });
        },
      },
    }) as any;
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }
}
