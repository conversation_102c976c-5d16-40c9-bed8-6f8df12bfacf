import { Injectable, NotFoundException } from "@nestjs/common";
import { LocationResponseDto } from "./dto/location-response.dto";
import { LocationRepository } from "./repositories/location.repository";

@Injectable()
export class LocationService {
  constructor(private readonly locationRepository: LocationRepository) {}

  async findAll(): Promise<LocationResponseDto[]> {
    const locations = await this.locationRepository.findAll();
    return locations.map(location => this.mapToResponseDto(location));
  }

  async findOne(id: string): Promise<LocationResponseDto> {
    const location = await this.locationRepository.findOne(id);
    if (!location) {
      throw new NotFoundException(`Location with ID "${id}" not found`);
    }
    return this.mapToResponseDto(location);
  }

  async findOneOrThrow(id: string): Promise<LocationResponseDto> {
    const location = await this.locationRepository.findOne(id);

    if (!location) {
      throw new NotFoundException(`Location with ID '${id}' not found`);
    }

    return this.mapToResponseDto(location);
  }

  private mapToResponseDto(location: any): LocationResponseDto {
    return {
      id: location.id,
      city: location.city,
      country: location.country,
    };
  }
}
