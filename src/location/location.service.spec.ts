import { NotFoundException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { LocationService } from "./location.service";
import { LocationRepository } from "./repositories/location.repository";

describe("LocationService", () => {
  let service: LocationService;
  let repository: LocationRepository;

  const mockLocation = { id: "1", city: "Test City", country: "Test Country" };
  const mockLocations = [mockLocation];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LocationService,
        {
          provide: LocationRepository,
          useValue: {
            findAll: jest.fn().mockResolvedValue(mockLocations),
            findOne: jest.fn().mockImplementation((id: string) => {
              if (id === "1") {
                return Promise.resolve(mockLocation);
              }
              return Promise.resolve(null);
            }),
          },
        },
      ],
    }).compile();

    service = module.get<LocationService>(LocationService);
    repository = module.get<LocationRepository>(LocationRepository);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("findAll", () => {
    it("should return an array of locations", async () => {
      const locations = await service.findAll();
      expect(locations).toEqual([{ id: "1", city: "Test City", country: "Test Country" }]);
      expect(repository.findAll).toHaveBeenCalled();
    });
  });

  describe("findOne", () => {
    it("should return a single location if found", async () => {
      const location = await service.findOne("1");
      expect(location).toEqual({ id: "1", city: "Test City", country: "Test Country" });
      expect(repository.findOne).toHaveBeenCalledWith("1");
    });

    it("should throw a NotFoundException if the location is not found", async () => {
      await expect(service.findOne("2")).rejects.toThrow(new NotFoundException("Location with ID \"2\" not found"));
    });
  });

  describe("findOneOrThrow", () => {
    it("should return a single location if found", async () => {
      const location = await service.findOneOrThrow("1");
      expect(location).toEqual({ id: "1", city: "Test City", country: "Test Country" });
      expect(repository.findOne).toHaveBeenCalledWith("1");
    });

    it("should throw a NotFoundException if the location is not found", async () => {
      await expect(service.findOneOrThrow("2")).rejects.toThrow(new NotFoundException("Location with ID '2' not found"));
    });
  });
});
