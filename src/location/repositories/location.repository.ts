import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma.service";

@Injectable()
export class LocationRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findAll() {
    return this.prisma.location.findMany({
      orderBy: [{ city: "asc" }, { country: "asc" }],
    });
  }

  async findOne(id: string) {
    return this.prisma.location.findUnique({
      where: { id },
    });
  }
}
