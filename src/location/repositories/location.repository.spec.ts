import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../../prisma.service";
import { LocationRepository } from "./location.repository";

describe("LocationRepository", () => {
  let repo: LocationRepository;
  let prisma: { location: { findMany: jest.Mock, findUnique: jest.Mock } };

  beforeEach(async () => {
    prisma = {
      location: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LocationRepository,
        { provide: PrismaService, useValue: prisma },
      ],
    }).compile();

    repo = module.get(LocationRepository);
  });

  it("findAll should return all locations sorted", async () => {
    prisma.location.findMany.mockResolvedValue([{ city: "A", country: "B", id: "l1" }]);
    const result = await repo.findAll();
    expect(prisma.location.findMany).toHaveBeenCalledWith({
      orderBy: [{ city: "asc" }, { country: "asc" }],
    });
    expect(result).toEqual([{ city: "A", country: "B", id: "l1" }]);
  });

  it("findOne should return location by id", async () => {
    prisma.location.findUnique.mockResolvedValue({ city: "C", country: "D", id: "l2" });
    const result = await repo.findOne("l2");
    expect(prisma.location.findUnique).toHaveBeenCalledWith({
      where: { id: "l2" },
    });
    expect(result).toEqual({ city: "C", country: "D", id: "l2" });
  });

  it("findOne should return undefined if not found", async () => {
    prisma.location.findUnique.mockImplementation(() => Promise.resolve());
    const result = await repo.findOne("not-exist");
    expect(prisma.location.findUnique).toHaveBeenCalledWith({
      where: { id: "not-exist" },
    });
    expect(result).toBeUndefined();
  });
});
