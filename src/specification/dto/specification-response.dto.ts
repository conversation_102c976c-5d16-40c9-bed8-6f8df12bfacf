import { ApiProperty } from "@nestjs/swagger";
import { SpecificationType } from "@/generated/prisma";

export class SpecificationResponseDto {
  @ApiProperty({
    description: "Unique identifier of the specification",
    example: "bf60be02-876f-48d3-875e-a79a2ccf3372",
  })
  id: string;

  @ApiProperty({
    description: "Property name",
    example: "mfi",
  })
  property: string;

  @ApiProperty({
    description: "Human-readable label for the property",
    example: "Melt Flow Index",
  })
  label: string;

  @ApiProperty({
    description: "Unit of measurement",
    example: "g/10min",
    required: false,
  })
  unit: string | null;

  @ApiProperty({
    description: "Type of specification - range for numeric ranges, text for text input",
    example: "RANGE",
    enum: SpecificationType,
  })
  type: SpecificationType;

  @ApiProperty({
    description: "Creation date",
    example: "2025-06-25T16:53:48.014Z",
  })
  createdAt: Date;

  @ApiProperty({
    description: "Last update date",
    example: "2025-06-25T16:53:48.014Z",
  })
  updatedAt: Date;
}
