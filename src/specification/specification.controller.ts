import {
  Controller,
  Get,
  Query,
  HttpStatus,
  UseGuards,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiOAuth2,
  ApiQuery,
} from "@nestjs/swagger";
import { AuthGuard } from "../auth/auth.guard";
import { PaginatedSpecificationResponseDto } from "./dto";
import { SpecificationService } from "./specification.service";

@ApiBearerAuth()
@ApiOAuth2([process.env.AZURE_API_SCOPE!])
@UseGuards(AuthGuard)
@ApiTags("specifications")
@Controller("specifications")
export class SpecificationController {
  constructor(private readonly specificationService: SpecificationService) {}

  @Get()
  @ApiOperation({ summary: "Get paginated list of specification" })
  @ApiQuery({ name: "search", required: false, description: "Search by label or property" })
  @ApiQuery({ name: "page", required: false, description: "Page number" })
  @ApiQuery({ name: "limit", required: false, description: "Items per page" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Specification criteria retrieved successfully",
    type: PaginatedSpecificationResponseDto,
  })
  async findAll(
    @Query("search") search?: string,
    @Query("page") page?: number,
    @Query("limit") limit?: number,
  ): Promise<PaginatedSpecificationResponseDto> {
    return this.specificationService.findAll({
      search,
      page,
      limit,
    });
  }
}
