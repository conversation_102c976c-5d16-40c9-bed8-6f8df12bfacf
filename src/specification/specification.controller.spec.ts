import { Test, TestingModule } from "@nestjs/testing";
import { AuthGuard } from "../auth/auth.guard";
import { SpecificationController } from "./specification.controller";
import { SpecificationService } from "./specification.service";
import { SpecificationType } from "@/generated/prisma";

describe("SpecificationController", () => {
  let controller: SpecificationController;
  let service: SpecificationService;

  const mockSpecificationService = {
    findAll: jest.fn(),
  };

  const mockAuthGuard = {
    canActivate: jest.fn().mockReturnValue(true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SpecificationController],
      providers: [
        {
          provide: SpecificationService,
          useValue: mockSpecificationService,
        },
      ],
    })
      .overrideGuard(AuthGuard)
      .useValue(mockAuthGuard)
      .compile();

    controller = module.get<SpecificationController>(SpecificationController);
    service = module.get<SpecificationService>(SpecificationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });

  describe("findAll", () => {
    it("should return paginated specifications with all query parameters", async () => {
      const expectedResult = {
        data: [
          {
            id: "spec-1",
            property: "tensileStrength",
            label: "Tensile Strength",
            unit: "MPa",
            type: SpecificationType.RANGE,
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
          {
            id: "spec-2",
            property: "meltFlowIndex",
            label: "Melt Flow Index",
            unit: "g/10min",
            type: SpecificationType.RANGE,
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          total: 2,
          perPage: 10,
          currentPage: 1,
          lastPage: 1,
          from: 1,
          to: 2,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll("strength", 1, 10);

      expect(service.findAll).toHaveBeenCalledWith({
        search: "strength",
        page: 1,
        limit: 10,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should return paginated specifications with search parameter only", async () => {
      const expectedResult = {
        data: [
          {
            id: "spec-1",
            property: "density",
            label: "Density",
            unit: "g/cm³",
            type: SpecificationType.RANGE,
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          total: 1,
          perPage: 10,
          currentPage: 1,
          lastPage: 1,
          from: 1,
          to: 1,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll("density");

      expect(service.findAll).toHaveBeenCalledWith({
        search: "density",
        page: undefined,
        limit: undefined,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should return paginated specifications with pagination parameters only", async () => {
      const expectedResult = {
        data: [
          {
            id: "spec-1",
            property: "temperature",
            label: "Temperature",
            unit: "°C",
            type: SpecificationType.RANGE,
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          total: 15,
          perPage: 5,
          currentPage: 2,
          lastPage: 3,
          from: 6,
          to: 10,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll(undefined, 2, 5);

      expect(service.findAll).toHaveBeenCalledWith({
        search: undefined,
        page: 2,
        limit: 5,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should return paginated specifications with no query parameters", async () => {
      const expectedResult = {
        data: [
          {
            id: "spec-1",
            property: "viscosity",
            label: "Viscosity",
            unit: "Pa⋅s",
            type: SpecificationType.RANGE,
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
          {
            id: "spec-2",
            property: "color",
            label: "Color",
            unit: null,
            type: SpecificationType.TEXT,
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          total: 2,
          perPage: 10,
          currentPage: 1,
          lastPage: 1,
          from: 1,
          to: 2,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll();

      expect(service.findAll).toHaveBeenCalledWith({
        search: undefined,
        page: undefined,
        limit: undefined,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle empty search results", async () => {
      const expectedResult = {
        data: [],
        meta: {
          total: 0,
          perPage: 10,
          currentPage: 1,
          lastPage: 0,
          from: 0,
          to: 0,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll("nonexistent", 1, 10);

      expect(service.findAll).toHaveBeenCalledWith({
        search: "nonexistent",
        page: 1,
        limit: 10,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle case-insensitive search", async () => {
      const expectedResult = {
        data: [
          {
            id: "spec-1",
            property: "tensileStrength",
            label: "Tensile Strength",
            unit: "MPa",
            type: SpecificationType.RANGE,
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          total: 1,
          perPage: 10,
          currentPage: 1,
          lastPage: 1,
          from: 1,
          to: 1,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll("TENSILE", 1, 10);

      expect(service.findAll).toHaveBeenCalledWith({
        search: "TENSILE",
        page: 1,
        limit: 10,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle specifications with different types", async () => {
      const expectedResult = {
        data: [
          {
            id: "spec-range",
            property: "pressure",
            label: "Pressure",
            unit: "bar",
            type: SpecificationType.RANGE,
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
          {
            id: "spec-text",
            property: "grade",
            label: "Grade",
            unit: null,
            type: SpecificationType.TEXT,
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          total: 2,
          perPage: 10,
          currentPage: 1,
          lastPage: 1,
          from: 1,
          to: 2,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll(undefined, 1, 10);

      expect(service.findAll).toHaveBeenCalledWith({
        search: undefined,
        page: 1,
        limit: 10,
      });
      expect(result).toEqual(expectedResult);
      expect(result.data[0].type).toBe(SpecificationType.RANGE);
      expect(result.data[1].type).toBe(SpecificationType.TEXT);
    });

    it("should handle large page numbers", async () => {
      const expectedResult = {
        data: [],
        meta: {
          total: 50,
          perPage: 10,
          currentPage: 100,
          lastPage: 5,
          from: 991,
          to: 1000,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll(undefined, 100, 10);

      expect(service.findAll).toHaveBeenCalledWith({
        search: undefined,
        page: 100,
        limit: 10,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle specifications with special characters in search", async () => {
      const expectedResult = {
        data: [
          {
            id: "spec-1",
            property: "elasticModulus",
            label: "Elastic Modulus (E-Modulus)",
            unit: "GPa",
            type: SpecificationType.RANGE,
            createdAt: new Date("2024-01-01T00:00:00.000Z"),
            updatedAt: new Date("2024-01-02T00:00:00.000Z"),
          },
        ],
        meta: {
          total: 1,
          perPage: 10,
          currentPage: 1,
          lastPage: 1,
          from: 1,
          to: 1,
        },
      };

      mockSpecificationService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll("E-Modulus", 1, 10);

      expect(service.findAll).toHaveBeenCalledWith({
        search: "E-Modulus",
        page: 1,
        limit: 10,
      });
      expect(result).toEqual(expectedResult);
    });
  });
});
