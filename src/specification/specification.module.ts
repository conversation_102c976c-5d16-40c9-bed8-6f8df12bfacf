import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { PrismaService } from "../prisma.service";
import { SpecificationRepository } from "./repositories/specification.repository";
import { SpecificationController } from "./specification.controller";
import { SpecificationService } from "./specification.service";
import { AuthService } from "@/auth/auth.service";

@Module({
  controllers: [SpecificationController],
  providers: [SpecificationService, SpecificationRepository, PrismaService, AuthService],
  exports: [SpecificationService],
})
export class SpecificationModule {}
