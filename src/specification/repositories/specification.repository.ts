import { Injectable } from "@nestjs/common";
import { calculatePaginationOffset } from "../../common/utils";
import { PrismaService } from "../../prisma.service";
import type { Specification } from "@/generated/prisma";

export interface CreateSpecificationData {
  property: string
  label: string
  unit?: string
}

export interface UpdateSpecificationData {
  property?: string
  label?: string
  unit?: string
}

export interface SpecificationFilters {
  search?: string
  page?: number
  limit?: number
}

export interface PaginatedSpecificationResult {
  data: Specification[]
  total: number
  page: number
  limit: number
}

@Injectable()
export class SpecificationRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findAll(filters: SpecificationFilters): Promise<PaginatedSpecificationResult> {
    const { page = 1, limit = 10, ...filterParameters } = filters;
    const offset = calculatePaginationOffset(page, limit);

    const where: any = {};

    if (filterParameters.search) {
      where.OR = [
        { label: { contains: filterParameters.search, mode: "insensitive" } },
        { property: { contains: filterParameters.search, mode: "insensitive" } },
      ];
    }

    const [data, total] = await Promise.all([
      this.prisma.specification.findMany({
        where,
        skip: offset,
        take: limit,
        orderBy: {
          updatedAt: "desc",
        },
      }),
      this.prisma.specification.count({ where }),
    ]);

    return {
      data,
      total,
      page,
      limit,
    };
  }

  async findById(id: string): Promise<Specification | null> {
    return this.prisma.specification.findUnique({
      where: { id },
    });
  }
}
