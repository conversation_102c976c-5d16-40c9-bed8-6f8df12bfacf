import { Test, TestingModule } from "@nestjs/testing";
import { SpecificationRepository } from "./repositories/specification.repository";
import { SpecificationService } from "./specification.service";
import { SpecificationType } from "@/generated/prisma";

describe("SpecificationService", () => {
  let service: SpecificationService;
  let repository: SpecificationRepository;

  const mockSpecificationRepository = {
    findAll: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SpecificationService,
        {
          provide: SpecificationRepository,
          useValue: mockSpecificationRepository,
        },
      ],
    }).compile();

    service = module.get<SpecificationService>(SpecificationService);
    repository = module.get<SpecificationRepository>(SpecificationRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("findAll", () => {
    it("should return paginated specifications with search filter", async () => {
      const mockSpecifications = [
        {
          id: "spec-1",
          property: "tensileStrength",
          label: "Tensile Strength",
          unit: "MPa",
          type: SpecificationType.RANGE,
          createdAt: new Date("2024-01-01T00:00:00.000Z"),
          updatedAt: new Date("2024-01-02T00:00:00.000Z"),
        },
        {
          id: "spec-2",
          property: "meltFlowIndex",
          label: "Melt Flow Index",
          unit: "g/10min",
          type: SpecificationType.RANGE,
          createdAt: new Date("2024-01-01T00:00:00.000Z"),
          updatedAt: new Date("2024-01-02T00:00:00.000Z"),
        },
      ];

      const mockRepositoryResult = {
        data: mockSpecifications,
        total: 2,
        page: 1,
        limit: 10,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        search: "tensile",
        page: 1,
        limit: 10,
      };

      const result = await service.findAll(filters);

      expect(repository.findAll).toHaveBeenCalledWith({
        search: "tensile",
        page: 1,
        limit: 10,
      });

      expect(result.data).toHaveLength(2);
      expect(result.data[0]).toEqual({
        id: "spec-1",
        property: "tensileStrength",
        label: "Tensile Strength",
        unit: "MPa",
        type: SpecificationType.RANGE,
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      });

      expect(result.meta).toEqual({
        total: 2,
        perPage: 10,
        currentPage: 1,
        lastPage: 1,
        from: 1,
        to: 2,
      });
    });

    it("should return paginated specifications without search filter", async () => {
      const mockSpecifications = [
        {
          id: "spec-1",
          property: "density",
          label: "Density",
          unit: "g/cm³",
          type: SpecificationType.RANGE,
          createdAt: new Date("2024-01-01T00:00:00.000Z"),
          updatedAt: new Date("2024-01-02T00:00:00.000Z"),
        },
      ];

      const mockRepositoryResult = {
        data: mockSpecifications,
        total: 1,
        page: 1,
        limit: 10,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        page: 1,
        limit: 10,
      };

      const result = await service.findAll(filters);

      expect(repository.findAll).toHaveBeenCalledWith({
        search: undefined,
        page: 1,
        limit: 10,
      });

      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toEqual({
        id: "spec-1",
        property: "density",
        label: "Density",
        unit: "g/cm³",
        type: SpecificationType.RANGE,
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      });

      expect(result.meta).toEqual({
        total: 1,
        perPage: 10,
        currentPage: 1,
        lastPage: 1,
        from: 1,
        to: 1,
      });
    });

    it("should handle empty results", async () => {
      const mockRepositoryResult = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        search: "nonexistent",
        page: 1,
        limit: 10,
      };

      const result = await service.findAll(filters);

      expect(repository.findAll).toHaveBeenCalledWith({
        search: "nonexistent",
        page: 1,
        limit: 10,
      });

      expect(result.data).toHaveLength(0);
      expect(result.meta).toEqual({
        total: 0,
        perPage: 10,
        currentPage: 1,
        lastPage: 0,
        from: 0,
        to: 0,
      });
    });

    it("should normalize pagination parameters", async () => {
      const mockRepositoryResult = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        search: "test",
      };

      await service.findAll(filters);

      expect(repository.findAll).toHaveBeenCalledWith({
        search: "test",
        page: 1,
        limit: 10,
      });
    });

    it("should handle pagination with different page sizes", async () => {
      const mockSpecifications = Array.from({ length: 25 }, (_, index) => ({
        id: `spec-${index + 1}`,
        property: `property${index + 1}`,
        label: `Property ${index + 1}`,
        unit: "unit",
        type: SpecificationType.RANGE,
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      }));

      const mockRepositoryResult = {
        data: mockSpecifications.slice(0, 5),
        total: 25,
        page: 1,
        limit: 5,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        page: 1,
        limit: 5,
      };

      const result = await service.findAll(filters);

      expect(repository.findAll).toHaveBeenCalledWith({
        search: undefined,
        page: 1,
        limit: 5,
      });

      expect(result.data).toHaveLength(5);
      expect(result.meta).toEqual({
        total: 25,
        perPage: 5,
        currentPage: 1,
        lastPage: 5,
        from: 1,
        to: 5,
      });
    });

    it("should handle specifications with null unit values", async () => {
      const mockSpecifications = [
        {
          id: "spec-1",
          property: "color",
          label: "Color",
          unit: null,
          type: SpecificationType.TEXT,
          createdAt: new Date("2024-01-01T00:00:00.000Z"),
          updatedAt: new Date("2024-01-02T00:00:00.000Z"),
        },
      ];

      const mockRepositoryResult = {
        data: mockSpecifications,
        total: 1,
        page: 1,
        limit: 10,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        page: 1,
        limit: 10,
      };

      const result = await service.findAll(filters);

      expect(result.data[0]).toEqual({
        id: "spec-1",
        property: "color",
        label: "Color",
        unit: null,
        type: SpecificationType.TEXT,
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      });
    });

    it("should handle different specification types", async () => {
      const mockSpecifications = [
        {
          id: "spec-range",
          property: "temperature",
          label: "Temperature",
          unit: "°C",
          type: SpecificationType.RANGE,
          createdAt: new Date("2024-01-01T00:00:00.000Z"),
          updatedAt: new Date("2024-01-02T00:00:00.000Z"),
        },
        {
          id: "spec-text",
          property: "material",
          label: "Material Type",
          unit: null,
          type: SpecificationType.TEXT,
          createdAt: new Date("2024-01-01T00:00:00.000Z"),
          updatedAt: new Date("2024-01-02T00:00:00.000Z"),
        },
      ];

      const mockRepositoryResult = {
        data: mockSpecifications,
        total: 2,
        page: 1,
        limit: 10,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const result = await service.findAll({ page: 1, limit: 10 });

      expect(result.data).toHaveLength(2);
      expect(result.data[0].type).toBe(SpecificationType.RANGE);
      expect(result.data[1].type).toBe(SpecificationType.TEXT);
    });

    it("should handle special characters in search query", async () => {
      const mockRepositoryResult = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        search: "test@#$%^&*()_+",
        page: 1,
        limit: 10,
      };

      const result = await service.findAll(filters);

      expect(repository.findAll).toHaveBeenCalledWith({
        search: "test@#$%^&*()_+",
        page: 1,
        limit: 10,
      });

      expect(result.data).toHaveLength(0);
    });

    it("should handle unicode characters in search query", async () => {
      const mockRepositoryResult = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const filters = {
        search: "测试данные🔬",
        page: 1,
        limit: 10,
      };

      const result = await service.findAll(filters);

      expect(repository.findAll).toHaveBeenCalledWith({
        search: "测试данные🔬",
        page: 1,
        limit: 10,
      });

      expect(result.data).toHaveLength(0);
    });
  });

  describe("error handling", () => {
    it("should propagate repository errors for findAll", async () => {
      const repositoryError = new Error("Database connection failed");
      mockSpecificationRepository.findAll.mockRejectedValue(repositoryError);

      const filters = { page: 1, limit: 10 };

      await expect(service.findAll(filters)).rejects.toThrow("Database connection failed");
      expect(repository.findAll).toHaveBeenCalledWith({
        search: undefined,
        page: 1,
        limit: 10,
      });
    });

    it("should handle timeout errors gracefully", async () => {
      const timeoutError = new Error("Query timeout");
      mockSpecificationRepository.findAll.mockRejectedValue(timeoutError);

      const filters = { page: 1, limit: 10 };

      await expect(service.findAll(filters)).rejects.toThrow("Query timeout");
    });
  });

  describe("service initialization", () => {
    it("should be defined", () => {
      expect(service).toBeDefined();
    });

    it("should have repository injected", () => {
      expect(repository).toBeDefined();
    });
  });

  describe("mapToResponseDto", () => {
    it("should correctly map specification to response DTO", async () => {
      const mockSpecification = {
        id: "test-spec-id",
        property: "testProperty",
        label: "Test Property",
        unit: "test-unit",
        type: SpecificationType.RANGE,
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      };

      const mockRepositoryResult = {
        data: [mockSpecification],
        total: 1,
        page: 1,
        limit: 10,
      };

      mockSpecificationRepository.findAll.mockResolvedValue(mockRepositoryResult);

      const result = await service.findAll({ page: 1, limit: 10 });

      expect(result.data[0]).toEqual({
        id: "test-spec-id",
        property: "testProperty",
        label: "Test Property",
        unit: "test-unit",
        type: SpecificationType.RANGE,
        createdAt: new Date("2024-01-01T00:00:00.000Z"),
        updatedAt: new Date("2024-01-02T00:00:00.000Z"),
      });
    });
  });
});
