/* eslint-disable */
export default async () => {
    const t = {
        ["./formulation/dto/create-formulation.dto"]: await import("./formulation/dto/create-formulation.dto"),
        ["./formulation/dto/create-formulation-response.dto"]: await import("./formulation/dto/create-formulation-response.dto"),
        ["./formulation/dto/formulation-response.dto"]: await import("./formulation/dto/formulation-response.dto"),
        ["./common/dto/pagination-meta.dto"]: await import("./common/dto/pagination-meta.dto"),
        ["./common/dto/criteria-operator.dto"]: await import("./common/dto/criteria-operator.dto"),
        ["./formulation/dto/comparison-request.dto"]: await import("./formulation/dto/comparison-request.dto"),
        ["./formulation/dto/criteria.dto"]: await import("./formulation/dto/criteria.dto"),
        ["./formulation/dto/comparison-response.dto"]: await import("./formulation/dto/comparison-response.dto"),
        ["./role/role.types"]: await import("./role/role.types"),
        ["./user/dto/user-response.dto"]: await import("./user/dto/user-response.dto"),
        ["./material/dto/material-comparison-response.dto"]: await import("./material/dto/material-comparison-response.dto"),
        ["./material/dto/family-criteria.dto"]: await import("./material/dto/family-criteria.dto"),
        ["./material/dto/material-page-response.dto"]: await import("./material/dto/material-page-response.dto"),
        ["./material/dto/material-search-response.dto"]: await import("./material/dto/material-search-response.dto"),
        ["./notification/dto/notification-response.dto"]: await import("./notification/dto/notification-response.dto"),
        ["./request/dto/request-response.dto"]: await import("./request/dto/request-response.dto"),
        ["./request/dto/request-action.enum"]: await import("./request/dto/request-action.enum"),
        ["./specification/dto/specification-response.dto"]: await import("./specification/dto/specification-response.dto"),
        ["./formulation/dto/paginated-formulation-response.dto"]: await import("./formulation/dto/paginated-formulation-response.dto"),
        ["./material/dto/paginated-material-response.dto"]: await import("./material/dto/paginated-material-response.dto"),
        ["./material/dto/paginated-material-page-response.dto"]: await import("./material/dto/paginated-material-page-response.dto"),
        ["./notification/dto/paginated-notification-response.dto"]: await import("./notification/dto/paginated-notification-response.dto"),
        ["./user/dto/paginated-user-response.dto"]: await import("./user/dto/paginated-user-response.dto"),
        ["./request/dto/paginated-request-response.dto"]: await import("./request/dto/paginated-request-response.dto"),
        ["./specification/dto/paginated-specification-response.dto"]: await import("./specification/dto/paginated-specification-response.dto")
    };
    return { "@nestjs/swagger": { "models": [[import("./department/dto/department-response.dto"), { "DepartmentResponseDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String } } }], [import("./common/dto/pagination-meta.dto"), { "PaginationMetaDto": { total: { required: true, type: () => Number }, perPage: { required: true, type: () => Number }, currentPage: { required: true, type: () => Number }, lastPage: { required: true, type: () => Number }, from: { required: true, type: () => Number }, to: { required: true, type: () => Number } } }], [import("./formulation/dto/create-formulation.dto"), { "CreateFormulationMaterialDto": { materialId: { required: true, type: () => String, format: "uuid" }, grades: { required: true, type: () => Object } }, "TestResultGradeValueDto": { value: { required: true, type: () => Number }, minRange: { required: false, type: () => Number }, maxRange: { required: false, type: () => Number } }, "CreateFormulationTestResultDto": { testName: { required: true, type: () => String }, standard: { required: false, type: () => String }, condition: { required: false, type: () => String }, propertyName: { required: true, type: () => String }, grades: { required: true, type: () => Object } }, "CreateFormulationDto": { code: { required: true, type: () => String }, name: { required: true, type: () => String }, materials: { required: true, type: () => [t["./formulation/dto/create-formulation.dto"].CreateFormulationMaterialDto] }, testResults: { required: true, type: () => [t["./formulation/dto/create-formulation.dto"].CreateFormulationTestResultDto] } } }], [import("./formulation/dto/create-formulation-response.dto"), { "CreatedFormulationDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String }, grade: { required: true, type: () => String }, ownerId: { required: true, type: () => String } }, "CreateFormulationResponseDto": { data: { required: true, type: () => [t["./formulation/dto/create-formulation-response.dto"].CreatedFormulationDto] } } }], [import("./formulation/dto/formulation-response.dto"), { "FormulationMaterialDto": { materialId: { required: true, type: () => String }, materialType: { required: true, type: () => String }, reference: { required: true, type: () => String }, value: { required: true, type: () => Number } }, "FormulationTestResultDto": { id: { required: true, type: () => String }, testName: { required: true, type: () => String }, standard: { required: false, type: () => String }, condition: { required: false, type: () => String }, value: { required: true, type: () => Number }, minRange: { required: false, type: () => Number }, maxRange: { required: false, type: () => Number } }, "FormulationResponseDto": { formulationId: { required: true, type: () => String }, name: { required: true, type: () => String }, grade: { required: true, type: () => String }, ownerId: { required: true, type: () => String }, isAccessible: { required: true, type: () => Boolean }, materials: { required: true, type: () => [t["./formulation/dto/formulation-response.dto"].FormulationMaterialDto] }, testResults: { required: true, type: () => [t["./formulation/dto/formulation-response.dto"].FormulationTestResultDto] } } }], [import("./formulation/dto/formulation-filter.dto"), { "FormulationFilterDto": { search: { required: false, type: () => String }, name: { required: false, type: () => String }, grade: { required: false, type: () => String }, ownerId: { required: false, type: () => String }, page: { required: false, type: () => Number, default: 1, minimum: 1 }, limit: { required: false, type: () => Number, default: 10, minimum: 1, maximum: 100 } } }], [import("./formulation/dto/paginated-formulation-response.dto"), { "PaginatedFormulationResponseDto": { data: { required: true, type: () => [t["./formulation/dto/formulation-response.dto"].FormulationResponseDto] }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }], [import("./formulation/dto/criteria.dto"), { "CriteriaDto": { propertyName: { required: true, type: () => String }, tier: { required: false, type: () => Number }, operator: { required: true, enum: t["./common/dto/criteria-operator.dto"].CriteriaOperator }, value: { required: true, type: () => Object }, valueTo: { required: true, type: () => Number }, required: { required: true, type: () => Boolean } } }], [import("./formulation/dto/comparison-request.dto"), { "MaterialCriteriaDto": { materialId: { required: true, type: () => String }, minValue: { required: false, type: () => Number }, maxValue: { required: false, type: () => Number } }, "ComparisonRequestDto": { materialCriteria: { required: false, type: () => [t["./formulation/dto/comparison-request.dto"].MaterialCriteriaDto] }, page: { required: false, type: () => Number, default: 1, minimum: 1 }, limit: { required: false, type: () => Number, default: 10, minimum: 1 }, criteria: { required: false, type: () => [t["./formulation/dto/criteria.dto"].CriteriaDto] } } }], [import("./common/dto/error-response.dto"), { "ErrorResponseDto": { message: { required: true, type: () => String }, code: { required: true, type: () => String } } }], [import("./common/dto/pagination-query.dto"), { "PaginationQueryDto": { page: { required: false, type: () => Number, default: 1, minimum: 1 }, limit: { required: false, type: () => Number, default: 10, minimum: 1 } } }], [import("./common/dto/paginated-response.dto"), { "PaginatedResponseDto": { data: { required: true }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }], [import("./common/dto/user.dto"), { "UserDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String } } }], [import("./formulation/dto/comparison-response.dto"), { "TestResultDto": { id: { required: true, type: () => String }, testName: { required: true, type: () => String }, standard: { required: true, type: () => String, nullable: true }, condition: { required: true, type: () => String, nullable: true }, value: { required: true, type: () => Number }, minRange: { required: false, type: () => Number, nullable: true }, maxRange: { required: false, type: () => Number, nullable: true } }, "MaterialInFormulationDto": { materialId: { required: true, type: () => String }, materialType: { required: true, type: () => String }, reference: { required: true, type: () => String }, value: { required: true, type: () => Number }, searched: { required: true, type: () => Boolean } }, "FormulationComparisonDto": { formulationId: { required: true, type: () => String }, name: { required: true, type: () => String }, grade: { required: true, type: () => String }, isAccessible: { required: true, type: () => Boolean }, materials: { required: true, type: () => [t["./formulation/dto/comparison-response.dto"].MaterialInFormulationDto] }, matchingMaterialsCount: { required: true, type: () => Number }, testResults: { required: true, type: () => [t["./formulation/dto/comparison-response.dto"].TestResultDto] }, tier: { required: false, type: () => Number }, optionalCriteriaMatched: { required: true, type: () => Number }, totalOptionalCriteria: { required: true, type: () => Number } }, "ComparisonResponseDto": { data: { required: true, type: () => [t["./formulation/dto/comparison-response.dto"].FormulationComparisonDto] }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }], [import("./location/dto/location-response.dto"), { "LocationResponseDto": { id: { required: true, type: () => String }, city: { required: true, type: () => String }, country: { required: true, type: () => String } } }], [import("./role/dto/role-response.dto"), { "RoleResponseDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String } } }], [import("./user/dto/create-user.dto"), { "CreateUserDto": { name: { required: true, type: () => String }, email: { required: true, type: () => String, format: "email" }, locationId: { required: true, type: () => String, format: "uuid" }, departmentId: { required: true, type: () => String, format: "uuid" }, roleId: { required: true, type: () => String, format: "uuid" } } }], [import("./user/dto/update-user.dto"), { "UpdateUserDto": {} }], [import("./user/dto/user-filter.dto"), { "UserFilterDto": { locationId: { required: false, type: () => String, format: "uuid" }, departmentId: { required: false, type: () => String, format: "uuid" }, roleId: { required: false, type: () => String, format: "uuid" }, roleCode: { required: false, enum: t["./role/role.types"].UserRole }, search: { required: false, type: () => String } } }], [import("./user/dto/user-response.dto"), { "UserResponseDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String }, email: { required: true, type: () => String }, location: { required: true, type: () => String }, department: { required: true, type: () => String }, role: { required: true, type: () => String } } }], [import("./user/dto/paginated-user-response.dto"), { "PaginatedUserResponseDto": { data: { required: true, type: () => [t["./user/dto/user-response.dto"].UserResponseDto] }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }], [import("./material/dto/family-criteria.dto"), { "FamilyCriteriaDto": { propertyName: { required: true, type: () => String }, operator: { required: true, enum: t["./common/dto/criteria-operator.dto"].CriteriaOperator }, value: { required: false, type: () => Object }, minValue: { required: false, type: () => Number }, maxValue: { required: false, type: () => Number } } }], [import("./material/dto/material-comparison-request.dto"), { "MaterialComparisonRequestDto": { materialIds: { required: true, type: () => [String], format: "uuid" }, includeProperties: { required: false, type: () => [String] } } }], [import("./material/dto/material-comparison-response.dto"), { "MaterialComparisonDto": { id: { required: true, type: () => String }, reference: { required: true, type: () => String }, type: { required: true, type: () => String }, family: { required: true, type: () => Object }, status: { required: true, type: () => Object }, supplierName: { required: true, type: () => String }, supplierId: { required: true, type: () => String }, supplierBatchNumber: { required: true, type: () => String }, origin: { required: true, type: () => String }, properties: { required: true, type: () => Object } }, "MaterialComparisonResponseDto": { materials: { required: true, type: () => [t["./material/dto/material-comparison-response.dto"].MaterialComparisonDto] }, availableProperties: { required: true, type: () => [String] }, propertyMetadata: { required: true, type: () => Object } } }], [import("./material/dto/family-data/additive.dto"), { "AdditiveFamilyDataDto": { anonymizationCode: { required: false, type: () => String } } }], [import("./material/dto/family-data/elastomer.dto"), { "ElastomerFamilyDataDto": { density: { required: false, type: () => Number }, mfi190216: { required: false, type: () => Number }, codeanonymElasto: { required: false, type: () => String }, nIzod23: { required: false, type: () => Number }, flexModulus: { required: false, type: () => Number }, tracModulus100: { required: false, type: () => Number }, elongAtBreak: { required: false, type: () => Number }, mfi230216: { required: false, type: () => Number }, meltingPoint: { required: false, type: () => Number }, hdtB: { required: false, type: () => Number }, hdtA: { required: false, type: () => Number }, shoreA: { required: false, type: () => Number }, shoreD: { required: false, type: () => Number } } }], [import("./material/dto/family-data/filler.dto"), { "FillerFamilyDataDto": { codeanonymFiller: { required: false, type: () => String }, bet: { required: false, type: () => Number }, d50: { required: false, type: () => Number }, d95d05: { required: false, type: () => Number }, whiteness: { required: false, type: () => Number }, form: { required: false, type: () => String } } }], [import("./material/dto/family-data/base-polymer.dto"), { "BasePolymerFamilyDataDto": { resultUpdateDate: { required: false, type: () => String }, validationEngineering: { required: false, type: () => String }, tds: { required: false, type: () => String }, priceExw: { required: false, type: () => Number }, priceExwDate: { required: false, type: () => String }, comment: { required: false, type: () => String }, mfiNorme: { required: false, type: () => String }, mfiTestConditions: { required: false, type: () => String }, mfiAv: { required: false, type: () => Number }, mfiStdDv: { required: false, type: () => Number }, densityNorme: { required: false, type: () => String }, densityAv: { required: false, type: () => Number }, densityStdDv: { required: false, type: () => Number } } }], [import("./material/dto/family-data/polymer.dto"), { "PolymerFamilyDataDto": { tensileModulusNorme: { required: false, type: () => String }, tensileModulusCond: { required: false, type: () => String }, tensileModulusAv: { required: false, type: () => Number }, tensileModulusStdDv: { required: false, type: () => Number }, flexuralModulusNorme: { required: false, type: () => String }, flexuralModulusAv: { required: false, type: () => Number }, flexuralModulusStdDev: { required: false, type: () => Number }, flexuralStressFcAv: { required: false, type: () => Number }, flexuralStressFcStdDev: { required: false, type: () => Number }, stressBreakNorme: { required: false, type: () => String }, stressBreakAv: { required: false, type: () => Number }, stressBreakStdDv: { required: false, type: () => Number }, stressYieldAv: { required: false, type: () => Number }, stressYieldStdDv: { required: false, type: () => Number }, yieldStrainAv: { required: false, type: () => Number }, yieldStrainStdDv: { required: false, type: () => Number }, strainBreakNorme: { required: false, type: () => String }, strainBreakAv: { required: false, type: () => Number }, strainBreakStdDv: { required: false, type: () => Number }, nominalStrainBreakAv: { required: false, type: () => Number }, nominalStrainBreakStdDv: { required: false, type: () => Number }, notchedIzodNorme: { required: false, type: () => String }, notchedIzodAv: { required: false, type: () => Number }, notchedIzodStdDv: { required: false, type: () => Number }, notchedIzodFailureType: { required: false, type: () => String }, unnotchedIzodNorme: { required: false, type: () => String }, unnotchedIzodAv: { required: false, type: () => Number }, unnotchedIzodStdDv: { required: false, type: () => Number }, unnotchedIzodFailureType: { required: false, type: () => String }, hdtBNorme: { required: false, type: () => String }, hdtBAv: { required: false, type: () => Number }, hdtBStdDv: { required: false, type: () => Number } } }], [import("./material/dto/family-data/recycle-polymer.dto"), { "RecyclePolymerFamilyDataDto": { technicalProfileAvgValue: { required: false, type: () => String }, feedstockOrUseInCompounds: { required: false, type: () => String }, color: { required: false, type: () => String }, pirPcrElv: { required: false, type: () => String }, wasteDetails: { required: false, type: () => String }, materialForm: { required: false, type: () => String }, productVolumesKtY: { required: false, type: () => Number }, volumesAvailableForMactKtY: { required: false, type: () => Number }, certificatesReachRohs: { required: false, type: () => String }, endOfWasteStatus: { required: false, type: () => String }, filtrationLocation: { required: false, type: () => String }, systemFiltration: { required: false, type: () => String }, filtrationSize: { required: false, type: () => String }, quantityFilteredRemaining: { required: false, type: () => Number }, d22NbFiltersUsed: { required: false, type: () => Number }, d22NbFilterPerKgFeedstock: { required: false, type: () => Number }, d32QuantityScrap: { required: false, type: () => Number } } }], [import("./material/dto/material-search-response.dto"), { "MaterialSearchResponseDto": { id: { required: true, type: () => String }, type: { required: true, type: () => String }, reference: { required: true, type: () => String }, family: { required: true, type: () => String }, status: { required: true, type: () => Object }, supplier: { required: true, type: () => String, nullable: true }, origin: { required: true, type: () => String, nullable: true }, supplierBatchNumber: { required: true, type: () => String, nullable: true } } }], [import("./material/dto/material-page-response.dto"), { "MaterialPageResponseDto": { familyData: { required: false, type: () => Object } } }], [import("./material/dto/material-search-page-request.dto"), { "MaterialSearchPageRequestDto": { search: { required: false, type: () => String }, origin: { required: false, type: () => Object }, family: { required: false, type: () => Object }, status: { required: false, type: () => Object }, supplier: { required: false, type: () => String }, supplierBatchNumber: { required: false, type: () => String }, reference: { required: false, type: () => String }, page: { required: false, type: () => Number, minimum: 1 }, limit: { required: false, type: () => Number, minimum: 1, maximum: 100 }, familyCriteria: { required: false, type: () => [t["./material/dto/family-criteria.dto"].FamilyCriteriaDto] } } }], [import("./material/dto/material-search-request.dto"), { "MaterialSearchRequestDto": { search: { required: false, type: () => String }, origin: { required: false, type: () => String }, family: { required: false, type: () => Object }, status: { required: false, type: () => Object }, supplier: { required: false, type: () => String }, supplierBatchNumber: { required: false, type: () => String }, reference: { required: false, type: () => String }, page: { required: false, type: () => Number, default: 1, minimum: 1 }, limit: { required: false, type: () => Number, default: 10, minimum: 1, maximum: 100 } } }], [import("./material/dto/paginated-material-page-response.dto"), { "PaginatedMaterialPageResponseDto": { data: { required: true, type: () => [t["./material/dto/material-page-response.dto"].MaterialPageResponseDto] }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }], [import("./material/dto/paginated-material-response.dto"), { "PaginatedMaterialResponseDto": { data: { required: true, type: () => [t["./material/dto/material-search-response.dto"].MaterialSearchResponseDto] }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }], [import("./material/dto/update-material.dto"), { "UpdateMaterialDto": { type: { required: false, type: () => String }, reference: { required: false, type: () => String }, family: { required: true, type: () => Object }, supplierId: { required: false, type: () => String }, supplierBatchNumber: { required: true, type: () => String }, origin: { required: false, type: () => String }, status: { required: false, type: () => Object }, familyData: { required: false, type: () => Object } } }], [import("./notification/dto/notification-response.dto"), { "NotificationResponseDto": { id: { required: true, type: () => String }, title: { required: true, type: () => String }, message: { required: true, type: () => String }, type: { required: true, type: () => Object }, isRead: { required: true, type: () => Boolean }, isSeen: { required: true, type: () => Boolean }, readAt: { required: true, type: () => Date, nullable: true }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date } } }], [import("./notification/dto/paginated-notification-response.dto"), { "PaginatedNotificationResponseDto": { data: { required: true, type: () => [t["./notification/dto/notification-response.dto"].NotificationResponseDto] }, unreadCount: { required: true, type: () => Number } } }], [import("./notification/dto/notification-query.dto"), { "NotificationQueryDto": { type: { required: false, type: () => Object }, isRead: { required: false, type: () => Boolean } } }], [import("./notification/dto/mark-as-read.dto"), { "MarkAsReadDto": { ids: { required: true, type: () => [String], minItems: 1 } } }], [import("./request/dto/request-query.dto"), { "RequestQueryDto": { search: { required: false, type: () => String }, status: { required: false, type: () => Object } } }], [import("./request/dto/request-response.dto"), { "LocationDto": { id: { required: true, type: () => String }, city: { required: true, type: () => String }, country: { required: true, type: () => String } }, "DepartmentDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String } }, "RequesterDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String }, email: { required: true, type: () => String }, locationId: { required: true, type: () => String }, departmentId: { required: true, type: () => String }, roleId: { required: true, type: () => String }, createdAt: { required: true, type: () => String }, updatedAt: { required: true, type: () => String }, location: { required: true, type: () => t["./request/dto/request-response.dto"].LocationDto }, department: { required: true, type: () => t["./request/dto/request-response.dto"].DepartmentDto } }, "FormulationDto": { id: { required: true, type: () => String }, name: { required: true, type: () => String }, ownerId: { required: true, type: () => String }, grade: { required: true, type: () => String } }, "RequestResponseDto": { id: { required: true, type: () => String }, status: { required: true, type: () => Object }, requester: { required: true, type: () => t["./request/dto/request-response.dto"].RequesterDto }, formulation: { required: true, type: () => t["./request/dto/request-response.dto"].FormulationDto } } }], [import("./request/dto/paginated-request-response.dto"), { "PaginatedRequestResponseDto": { data: { required: true, type: () => [t["./request/dto/request-response.dto"].RequestResponseDto] } } }], [import("./request/dto/create-request.dto"), { "CreateRequestDto": { formulationId: { required: true, type: () => String, format: "uuid" } } }], [import("./request/dto/request-action.dto"), { "RequestActionDto": { action: { required: true, enum: t["./request/dto/request-action.enum"].RequestAction } } }], [import("./specification/dto/specification-response.dto"), { "SpecificationResponseDto": { id: { required: true, type: () => String }, property: { required: true, type: () => String }, label: { required: true, type: () => String }, unit: { required: true, type: () => String, nullable: true }, type: { required: true, type: () => Object }, createdAt: { required: true, type: () => Date }, updatedAt: { required: true, type: () => Date } } }], [import("./specification/dto/paginated-specification-response.dto"), { "PaginatedSpecificationResponseDto": { data: { required: true, type: () => [t["./specification/dto/specification-response.dto"].SpecificationResponseDto] }, meta: { required: true, type: () => t["./common/dto/pagination-meta.dto"].PaginationMetaDto } } }]], "controllers": [[import("./app.controller"), { "AppController": { "getHello": { type: String } } }], [import("./formulation/formulation.controller"), { "FormulationController": { "createFormulations": { type: t["./formulation/dto/create-formulation-response.dto"].CreateFormulationResponseDto }, "getFormulations": { type: t["./formulation/dto/paginated-formulation-response.dto"].PaginatedFormulationResponseDto }, "getFormulationById": { type: t["./formulation/dto/formulation-response.dto"].FormulationResponseDto }, "compareFormulations": { type: t["./formulation/dto/comparison-response.dto"].ComparisonResponseDto } } }], [import("./material/material.controller"), { "MaterialController": { "getMaterials": { type: t["./material/dto/paginated-material-response.dto"].PaginatedMaterialResponseDto }, "getMaterialOrigins": { type: [String] }, "getMaterialsPage": { type: t["./material/dto/paginated-material-page-response.dto"].PaginatedMaterialPageResponseDto }, "compareMaterials": { type: t["./material/dto/material-comparison-response.dto"].MaterialComparisonResponseDto }, "updateMaterial": { type: t["./material/dto/material-search-response.dto"].MaterialSearchResponseDto } } }], [import("./notification/notification.controller"), { "NotificationController": { "getNotifications": { type: t["./notification/dto/paginated-notification-response.dto"].PaginatedNotificationResponseDto }, "markAsRead": { type: [t["./notification/dto/notification-response.dto"].NotificationResponseDto] } } }], [import("./user/user.controller"), { "UserController": { "create": { type: t["./user/dto/user-response.dto"].UserResponseDto }, "findAll": { type: t["./user/dto/paginated-user-response.dto"].PaginatedUserResponseDto }, "getFilterOptions": {}, "getCurrentUserProfile": { type: t["./user/dto/user-response.dto"].UserResponseDto }, "findOne": { type: t["./user/dto/user-response.dto"].UserResponseDto }, "update": { type: t["./user/dto/user-response.dto"].UserResponseDto }, "remove": {} } }], [import("./request/request.controller"), { "RequestController": { "findAll": { type: t["./request/dto/paginated-request-response.dto"].PaginatedRequestResponseDto }, "create": { type: t["./request/dto/request-response.dto"].RequestResponseDto }, "handleAction": { type: t["./request/dto/request-response.dto"].RequestResponseDto }, "revokeAccess": {} } }], [import("./specification/specification.controller"), { "SpecificationController": { "findAll": { type: t["./specification/dto/paginated-specification-response.dto"].PaginatedSpecificationResponseDto } } }]] } };
};