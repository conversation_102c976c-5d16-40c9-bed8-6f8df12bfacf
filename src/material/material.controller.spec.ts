import { Test, TestingModule } from "@nestjs/testing";
import { AuthGuard } from "../auth/auth.guard";
import { RoleGuard } from "../auth/role.guard";
import { AuthenticatedRequest } from "../common";
import { UserRole } from "../role/role.types";
import { MaterialSearchPageRequestDto, MaterialSearchRequestDto, UpdateMaterialDto } from "./dto";
import { MaterialComparisonService } from "./material-comparison.service";
import { MaterialController } from "./material.controller";
import { MaterialService } from "./material.service";
import { CriteriaOperator } from "@/common/dto/criteria-operator.dto";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

describe("MaterialController", () => {
  let controller: MaterialController;

  const mockMaterialService = {
    getMaterialOrigins: jest.fn(),
    searchMaterials: jest.fn(),
    searchMaterialsWithCriteria: jest.fn(),
    updateMaterial: jest.fn(),
  };

  const mockMaterialComparisonService = {
    compareMaterials: jest.fn(),
  };

  const mockAuthGuard = {
    canActivate: jest.fn().mockReturnValue(true),
  };

  const mockRoleGuard = {
    canActivate: jest.fn().mockReturnValue(true),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      controllers: [MaterialController],
      providers: [
        {
          provide: MaterialService,
          useValue: mockMaterialService,
        },
        {
          provide: MaterialComparisonService,
          useValue: mockMaterialComparisonService,
        },
      ],
    })
      .overrideGuard(AuthGuard)
      .useValue(mockAuthGuard)
      .overrideGuard(RoleGuard)
      .useValue(mockRoleGuard)
      .compile();

    controller = module.get<MaterialController>(MaterialController);
  });

  describe("getMaterials", () => {
    it("should get materials by origin", async () => {
      const expectedResult = {
        data: [
          {
            family: "POLYMERS",
            id: "material-1",
            origin: "Europe",
            reference: "MAT-001",
            status: "AVAILABLE",
            supplier: "Test Supplier",
            supplierBatchNumber: "BATCH-123",
            type: "Polymer",
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockMaterialService.searchMaterials.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchRequestDto = { origin: "Europe" };
      const result = await controller.getMaterials(searchRequest);

      expect(mockMaterialService.searchMaterials).toHaveBeenCalledWith({
        origin: "Europe",
        userRole: undefined,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should get materials by search parameter", async () => {
      const expectedResult = {
        data: [
          {
            family: "POLYMERS",
            id: "material-1",
            origin: "Europe",
            reference: "MAT-001",
            status: "AVAILABLE",
            supplier: "Test Supplier",
            supplierBatchNumber: "BATCH-123",
            type: "Polymer",
          },
          {
            family: "POLYMERS",
            id: "material-2",
            origin: "Asia",
            reference: "MAT-002",
            status: "AVAILABLE",
            supplier: "Another Supplier",
            supplierBatchNumber: "BATCH-456",
            type: "Polymer Blend",
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 2,
          total: 2,
        },
      };

      mockMaterialService.searchMaterials.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchRequestDto = { search: "polymer" };
      const result = await controller.getMaterials(searchRequest);

      expect(mockMaterialService.searchMaterials).toHaveBeenCalledWith({
        search: "polymer",
        userRole: undefined,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should get materials without filters", async () => {
      const expectedResult = {
        data: [],
        meta: {
          currentPage: 1,
          from: 0,
          lastPage: 0,
          perPage: 10,
          to: 0,
          total: 0,
        },
      };

      mockMaterialService.searchMaterials.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchRequestDto = {};
      const result = await controller.getMaterials(searchRequest);

      expect(mockMaterialService.searchMaterials).toHaveBeenCalledWith({
        userRole: undefined,
      });
      expect(result).toEqual(expectedResult);
    });
  });

  describe("getMaterialOrigins", () => {
    it("should return distinct material origins", async () => {
      const expectedOrigins = ["Asia", "Europe", "North America"];

      mockMaterialService.getMaterialOrigins.mockResolvedValue(expectedOrigins);

      const result = await controller.getMaterialOrigins();

      expect(mockMaterialService.getMaterialOrigins).toHaveBeenCalledWith();
      expect(result).toEqual(expectedOrigins);
      expect(result).toHaveLength(3);
    });

    it("should return empty array when no origins exist", async () => {
      mockMaterialService.getMaterialOrigins.mockResolvedValue([]);

      const result = await controller.getMaterialOrigins();

      expect(mockMaterialService.getMaterialOrigins).toHaveBeenCalledWith();
      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it("should handle single origin", async () => {
      const expectedOrigins = ["Europe"];

      mockMaterialService.getMaterialOrigins.mockResolvedValue(expectedOrigins);

      const result = await controller.getMaterialOrigins();

      expect(mockMaterialService.getMaterialOrigins).toHaveBeenCalledWith();
      expect(result).toEqual(expectedOrigins);
      expect(result).toHaveLength(1);
    });
  });

  describe("getMaterialsPage", () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("should search materials with criteria and return materials with family data", async () => {
      const expectedResult = {
        data: [
          {
            family: MaterialFamily.POLYMERS,
            familyData: {
              densityAv: 0.95,
              mfiAv: 10.5,
              tensileModulusAv: 2500,
            },
            id: "material-1",
            origin: "Europe",
            reference: "MAT-001",
            status: MaterialStatus.AVAILABLE,
            supplier: "Test Supplier",
            supplierBatchNumber: "BATCH-123",
            type: "Polymer",
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {
        family: MaterialFamily.POLYMERS,
        search: "polymer",
        status: MaterialStatus.AVAILABLE,
      };

      const request = { user: { role: UserRole.ADMIN } } as AuthenticatedRequest;
      const result = await controller.getMaterialsPage(searchRequest, request);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        search: "polymer",
        status: MaterialStatus.AVAILABLE,
        userRole: UserRole.ADMIN,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle array filters for origin and status", async () => {
      const expectedResult = {
        data: [
          {
            family: MaterialFamily.POLYMERS,
            id: "material-1",
            origin: "Europe",
            reference: "MAT-001",
            status: MaterialStatus.AVAILABLE,
            supplier: "Test Supplier",
            type: "Polymer",
          },
          {
            family: MaterialFamily.POLYMERS,
            id: "material-2",
            origin: "Asia",
            reference: "MAT-002",
            status: MaterialStatus.ARCHIVE,
            supplier: "Another Supplier",
            type: "Polymer",
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 2,
          total: 2,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {
        family: MaterialFamily.POLYMERS,
        origin: ["Europe", "Asia"],
        status: [MaterialStatus.AVAILABLE, MaterialStatus.ARCHIVE],
      };

      const request = { user: { role: UserRole.MATERIAL_MANAGER } } as AuthenticatedRequest;
      const result = await controller.getMaterialsPage(searchRequest, request);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        origin: ["Europe", "Asia"],
        status: [MaterialStatus.AVAILABLE, MaterialStatus.ARCHIVE],
        userRole: UserRole.MATERIAL_MANAGER,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle family criteria filtering", async () => {
      const expectedResult = {
        data: [
          {
            family: MaterialFamily.POLYMERS,
            familyData: {
              densityAv: 0.98,
              mfiAv: 15.2,
              tensileModulusAv: 3000,
            },
            id: "material-1",
            reference: "MAT-001",
            status: MaterialStatus.AVAILABLE,
            type: "High-Performance Polymer",
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {
        family: MaterialFamily.POLYMERS,
        familyCriteria: [
          {
            operator: CriteriaOperator.GREATER_THAN_OR_EQUAL,
            propertyName: "mfiAv",
            value: 10,
          },
          {
            maxValue: 5000,
            minValue: 2000,
            operator: CriteriaOperator.BETWEEN,
            propertyName: "tensileModulusAv",
          },
        ],
      };

      const request = { user: { role: UserRole.ADMIN } } as AuthenticatedRequest;
      const result = await controller.getMaterialsPage(searchRequest, request);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        familyCriteria: [
          {
            operator: CriteriaOperator.GREATER_THAN_OR_EQUAL,
            propertyName: "mfiAv",
            value: 10,
          },
          {
            maxValue: 5000,
            minValue: 2000,
            operator: CriteriaOperator.BETWEEN,
            propertyName: "tensileModulusAv",
          },
        ],
        userRole: UserRole.ADMIN,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle FEEDSTOCK_RECYCLING_MEMBERS role with recycle polymers", async () => {
      const expectedResult = {
        data: [
          {
            family: MaterialFamily.RECYCLE_POLYMERS,
            familyData: {
              color: "Natural",
              mfiAv: 8.5,
              pirPcrElv: "PCR",
            },
            id: "recycle-material-1",
            reference: "REC-001",
            status: MaterialStatus.AVAILABLE,
            type: "Recycled Polymer",
          },
        ],
        meta: {
          currentPage: 1,
          from: 1,
          lastPage: 1,
          perPage: 10,
          to: 1,
          total: 1,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {
        search: "recycled",
      };

      const request = { user: { role: UserRole.FEEDSTOCK_RECYCLING_MEMBERS } } as AuthenticatedRequest;
      const result = await controller.getMaterialsPage(searchRequest, request);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        search: "recycled",
        userRole: UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle pagination parameters", async () => {
      const expectedResult = {
        data: [],
        meta: {
          currentPage: 3,
          from: 11,
          lastPage: 10,
          perPage: 5,
          to: 15,
          total: 50,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {
        family: MaterialFamily.POLYMERS,
        limit: 5,
        page: 3,
      };

      const request = { user: { role: UserRole.ADMIN } } as AuthenticatedRequest;
      const result = await controller.getMaterialsPage(searchRequest, request);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        limit: 5,
        page: 3,
        userRole: UserRole.ADMIN,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle empty request with default pagination", async () => {
      const expectedResult = {
        data: [],
        meta: {
          currentPage: 1,
          from: 0,
          lastPage: 0,
          perPage: 10,
          to: 0,
          total: 0,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {};
      const request = { user: { role: UserRole.ADMIN } } as AuthenticatedRequest;
      const result = await controller.getMaterialsPage(searchRequest, request);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        userRole: UserRole.ADMIN,
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle request without user context", async () => {
      const expectedResult = {
        data: [],
        meta: {
          currentPage: 1,
          from: 0,
          lastPage: 0,
          perPage: 10,
          to: 0,
          total: 0,
        },
      };

      mockMaterialService.searchMaterialsWithCriteria.mockResolvedValue(expectedResult);

      const searchRequest: MaterialSearchPageRequestDto = {
        search: "test",
      };

      const result = await controller.getMaterialsPage(searchRequest);

      expect(mockMaterialService.searchMaterialsWithCriteria).toHaveBeenCalledWith({
        search: "test",
        userRole: undefined,
      });
      expect(result).toEqual(expectedResult);
    });
  });

  describe("updateMaterial", () => {
    it("should update material successfully", async () => {
      const materialId = "material-1";
      const updateDto: UpdateMaterialDto = {
        family: MaterialFamily.POLYMERS,
        origin: "Updated Origin",
        status: MaterialStatus.AVAILABLE,
        supplierBatchNumber: "BATCH-123",
        type: "Updated Polypropylene",
      };

      const expectedResult = {
        family: "POLYMERS",
        id: materialId,
        origin: "Updated Origin",
        reference: "MAT-001",
        status: MaterialStatus.AVAILABLE,
        supplier: "Test Supplier",
        supplierBatchNumber: "BATCH-123",
        type: "Updated Polypropylene",
      };

      mockMaterialService.updateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: UserRole.ADMIN } } as AuthenticatedRequest;
      const result = await controller.updateMaterial(materialId, updateDto, mockRequest);

      expect(mockMaterialService.updateMaterial).toHaveBeenCalledWith(materialId, updateDto, UserRole.ADMIN);
      expect(result).toEqual(expectedResult);
    });

    it("should update material with partial data", async () => {
      const materialId = "material-2";
      const updateDto: UpdateMaterialDto = {
        family: MaterialFamily.FILLERS,
        status: MaterialStatus.ARCHIVE,
        supplierBatchNumber: "BATCH-456",
      };

      const expectedResult = {
        family: "FILLERS",
        id: materialId,
        origin: "Asia",
        reference: "MAT-002",
        status: MaterialStatus.ARCHIVE,
        supplier: "Filler Supplier",
        supplierBatchNumber: "BATCH-456",
        type: "Filler Material",
      };

      mockMaterialService.updateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: UserRole.MATERIAL_MANAGER } } as AuthenticatedRequest;
      const result = await controller.updateMaterial(materialId, updateDto, mockRequest);

      expect(mockMaterialService.updateMaterial).toHaveBeenCalledWith(materialId, updateDto, UserRole.MATERIAL_MANAGER);
      expect(result).toEqual(expectedResult);
    });

    it("should handle empty update DTO", async () => {
      const materialId = "material-3";
      const updateDto: UpdateMaterialDto = {
        family: MaterialFamily.ELASTOMERS,
        supplierBatchNumber: "BATCH-789",
      };

      const expectedResult = {
        family: "ELASTOMERS",
        id: materialId,
        origin: "South America",
        reference: "MAT-003",
        status: MaterialStatus.AVAILABLE,
        supplier: "Elastomer Supplier",
        supplierBatchNumber: "BATCH-789",
        type: "Elastomer Material",
      };

      mockMaterialService.updateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: UserRole.ADMIN } } as AuthenticatedRequest;
      const result = await controller.updateMaterial(materialId, updateDto, mockRequest);

      expect(mockMaterialService.updateMaterial).toHaveBeenCalledWith(materialId, updateDto, UserRole.ADMIN);
      expect(result).toEqual(expectedResult);
    });

    it("should update all material fields", async () => {
      const materialId = "material-4";
      const updateDto: UpdateMaterialDto = {
        family: MaterialFamily.ADDITIVES,
        origin: "North America",
        reference: "NEW-REF-001",
        status: MaterialStatus.UNDER_REVIEW,
        supplierBatchNumber: "NEW-BATCH-789",
        supplierId: "new-supplier-id",
        type: "New Material Type",
      };

      const expectedResult = {
        family: "ADDITIVES",
        id: materialId,
        origin: "North America",
        reference: "NEW-REF-001",
        status: MaterialStatus.UNDER_REVIEW,
        supplier: "New Supplier",
        supplierBatchNumber: "NEW-BATCH-789",
        type: "New Material Type",
      };

      mockMaterialService.updateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: UserRole.ADMIN } } as AuthenticatedRequest;
      const result = await controller.updateMaterial(materialId, updateDto, mockRequest);

      expect(mockMaterialService.updateMaterial).toHaveBeenCalledWith(materialId, updateDto, UserRole.ADMIN);
      expect(result).toEqual(expectedResult);
    });

    it("should handle material with null supplier", async () => {
      const materialId = "material-5";
      const updateDto: UpdateMaterialDto = {
        family: MaterialFamily.ADDITIVES,
        origin: "Unknown",
        supplierBatchNumber: "BATCH-000",
        type: "Updated Material with No Supplier",
      };

      const expectedResult = {
        family: "ADDITIVES",
        id: materialId,
        origin: "Unknown",
        reference: "MAT-005",
        status: MaterialStatus.AVAILABLE,
        supplier: undefined,
        supplierBatchNumber: "BATCH-000",
        type: "Updated Material with No Supplier",
      };

      mockMaterialService.updateMaterial.mockResolvedValue(expectedResult);

      const mockRequest = { user: { role: UserRole.MATERIAL_MANAGER } } as AuthenticatedRequest;
      const result = await controller.updateMaterial(materialId, updateDto, mockRequest);

      expect(mockMaterialService.updateMaterial).toHaveBeenCalledWith(materialId, updateDto, UserRole.MATERIAL_MANAGER);
      expect(result).toEqual(expectedResult);
    });
  });
});
