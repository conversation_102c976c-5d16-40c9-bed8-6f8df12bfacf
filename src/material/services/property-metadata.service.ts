import { Injectable } from "@nestjs/common";
import { PropertyMetadataDto } from "../dto";
import { PropertyMetadataRepository } from "../repositories/property-metadata.repository";
import { MaterialFamily, PropertyType } from "@/generated/prisma";

export type PropertyMetadataMap = Record<string, {
  unit?: string | null
  description: string
  type: PropertyType
}>;

@Injectable()
export class PropertyMetadataService {
  constructor(private readonly propertyMetadataRepository: PropertyMetadataRepository) {}

  /**
   * Get property metadata for a specific material family as a map
   * @param family - The material family to get metadata for
   * @returns Property metadata map for the specified family
   */
  async getPropertyMetadataByFamily(family: MaterialFamily): Promise<PropertyMetadataMap> {
    const properties = await this.propertyMetadataRepository.findByFamily(family);
    return this.convertToMap(properties);
  }

  /**
   * Get consolidated property metadata for multiple material families
   * @param families - Array of material families to get metadata for
   * @returns Consolidated property metadata map
   */
  async getConsolidatedPropertyMetadata(families: MaterialFamily[]): Promise<PropertyMetadataMap> {
    const properties = await this.propertyMetadataRepository.findByFamilies(families);
    return this.convertToMap(properties);
  }

  /**
   * Convert array of PropertyMetadataDto to PropertyMetadataMap
   * @param properties - Array of property metadata DTOs
   * @returns Property metadata map
   */
  private convertToMap(properties: PropertyMetadataDto[]): PropertyMetadataMap {
    const map: PropertyMetadataMap = {};
    for (const property of properties) {
      map[property.propertyKey] = {
        unit: property.unit,
        description: property.description,
        type: property.type,
      };
    }
    return map;
  }
}
