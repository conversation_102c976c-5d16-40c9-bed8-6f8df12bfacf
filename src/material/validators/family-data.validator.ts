import {
  registerDecorator,
  validateSync,
  ValidationArguments,
  ValidationError,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from "class-validator";
import { AdditiveFamilyDataDto } from "../dto/family-data/additive.dto";
import { ElastomerFamilyDataDto } from "../dto/family-data/elastomer.dto";
import { FillerFamilyDataDto } from "../dto/family-data/filler.dto";
import { PolymerFamilyDataDto } from "../dto/family-data/polymer.dto";
import { RecyclePolymerFamilyDataDto } from "../dto/family-data/recycle-polymer.dto";
import { MaterialFamily } from "@/generated/prisma";

const familyDataMap = {
  [MaterialFamily.POLYMERS]: PolymerFamilyDataDto,
  [MaterialFamily.FILLERS]: FillerFamilyDataDto,
  [MaterialFamily.ELASTOMERS]: ElastomerFamilyDataDto,
  [MaterialFamily.ADDITIVES]: AdditiveFamilyDataDto,
  [MaterialFamily.RECYCLE_POLYMERS]: RecyclePolymerFamilyDataDto,
};

@ValidatorConstraint({ async: false, name: "ValidateFamilyData" })
class ValidateFamilyDataConstraint implements ValidatorConstraintInterface {
  private validationErrors: string[] = [];

  public validate(value: unknown, validationArguments: ValidationArguments): boolean {
    if (!value) return true;

    const object = validationArguments.object as Record<string, unknown>;
    const family = object.family as MaterialFamily;

    if (!family) {
      this.validationErrors = ["Family must be specified when providing familyData"];
      return false;
    }

    const familyDto = familyDataMap[family];
    if (!familyDto) {
      this.validationErrors = [`No validation available for family: ${family}`];
      return false;
    }

    return this.validateFamilyData(value, familyDto, family);
  }

  private validateFamilyData(value: unknown, familyDtoClass: new () => object, family: MaterialFamily): boolean {
    const instance = Object.assign(new familyDtoClass(), value);
    const errors = validateSync(instance, {
      forbidNonWhitelisted: true,
      whitelist: true,
    });

    if (errors.length === 0) {
      return true;
    }

    this.validationErrors = [];
    this.processValidationErrors(errors, family);
    return false;
  }

  private processValidationErrors(errors: ValidationError[], family: MaterialFamily): void {
    const invalidProperties: string[] = [];
    const otherErrors: string[] = [];

    for (const error of errors) {
      if (error.constraints) {
        for (const constraint of Object.values(error.constraints)) {
          if (constraint.includes("should not exist")) {
            invalidProperties.push(error.property);
          }
          else {
            otherErrors.push(constraint);
          }
        }
      }
    }

    if (invalidProperties.length > 0) {
      this.validationErrors.push(this.formatInvalidPropertiesMessage(invalidProperties, family));
    }

    this.validationErrors.push(...otherErrors);
  }

  private categorizeErrors(validationErrors: ValidationError[]): string[] {
    const errors: string[] = [];
    for (const error of validationErrors) {
      if (error.constraints) {
        errors.push(...Object.values(error.constraints));
      }
    }
    return errors;
  }

  private formatInvalidPropertiesMessage(properties: string[], family: string): string {
    if (properties.length === 1) {
      return `Property '${properties[0]}' is not valid for family '${family}'`;
    }

    if (properties.length === 2) {
      return `Property '${properties[0]}' and '${properties[1]}' are not valid for family '${family}'`;
    }

    const lastProperty = properties.at(-1);
    const otherProperties = properties.slice(0, -1).map(property => `'${property}'`).join(", ");
    return `Property ${otherProperties}, and '${lastProperty}' are not valid for family '${family}'`;
  }

  public defaultMessage(): string {
    return this.validationErrors.join(", ");
  }
}

/**
 * Decorator to validate familyData based on the material family type
 * @param validationOptions - Optional validation options
 * @returns Property decorator function
 */
export function validateFamilyData(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      constraints: [],
      options: validationOptions,
      propertyName: propertyName,
      target: object.constructor,
      validator: ValidateFamilyDataConstraint,
    });
  };
}
