import { Injectable, BadRequestException, NotFoundException, UnprocessableEntityException } from "@nestjs/common";
import { UserRole } from "../role/role.types";
import { MaterialComparisonRequestDto, MaterialComparisonResponseDto, MaterialComparisonDto } from "./dto";
import { MaterialComparisonRepository } from "./repositories/material-comparison.repository";
import { PropertyMetadataService } from "./services/property-metadata.service";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

@Injectable()
export class MaterialComparisonService {
  constructor(
    private readonly materialComparisonRepository: MaterialComparisonRepository,
    private readonly propertyMetadataService: PropertyMetadataService,
  ) {}

  /**
   * Compare materials based on provided IDs and optional properties
   * @param request - Request DTO containing material IDs and optional properties
   * @param userRole - Role of the user making the request
   * @returns Material comparison response DTO
   */
  async compareMaterials(request: MaterialComparisonRequestDto, userRole?: UserRole): Promise<MaterialComparisonResponseDto> {
    const { materialIds, includeProperties } = request;

    if (materialIds.length === 0) {
      throw new BadRequestException("At least one material ID must be provided");
    }

    if (materialIds.length > 20) {
      throw new UnprocessableEntityException("Maximum 20 materials can be compared at once");
    }

    const materials = userRole === UserRole.ENGINEERS
      ? await this.materialComparisonRepository.getMaterialsForComparison(materialIds, MaterialStatus.AVAILABLE)
      : await this.materialComparisonRepository.getMaterialsForComparison(materialIds);

    if (materials.length === 0) {
      throw new NotFoundException("No materials found with the provided IDs");
    }

    if (materials.length !== materialIds.length) {
      const foundIds = new Set(materials.map(m => m.id));
      const missingIds = materialIds.filter(id => !foundIds.has(id));

      const errorMessage = userRole === UserRole.ENGINEERS
        ? `Materials not found or not available: ${missingIds.join(", ")}. Engineers can only compare available materials.`
        : `Materials not found: ${missingIds.join(", ")}`;

      throw new NotFoundException(errorMessage);
    }

    const filteredMaterials = includeProperties
      ? this.filterMaterialProperties(materials, includeProperties)
      : materials;

    const familiesInComparison = [...new Set(materials.map(m => m.family))];

    const availableProperties = includeProperties || await this.getAllAvailableProperties(familiesInComparison);

    const propertyMetadata = includeProperties
      ? await this.getFilteredPropertyMetadata(familiesInComparison, includeProperties)
      : await this.getAllPropertyMetadata(familiesInComparison);

    return {
      materials: filteredMaterials,
      availableProperties,
      propertyMetadata,
    };
  }

  /**
   * Filter material properties based on requested properties
   * @param materials - Array of material comparison DTOs
   * @param includeProperties - Array of property keys to validate
   * @returns Filtered materials with only requested properties
   */
  private filterMaterialProperties(
    materials: MaterialComparisonDto[],
    includeProperties: string[],
  ): MaterialComparisonDto[] {
    return materials.map(material => ({
      ...material,
      properties: Object.fromEntries(
        Object.entries(material.properties || {}).filter(([key]) =>
          includeProperties.includes(key),
        ),
      ),
    }));
  }

  /**
   * Get all available properties for material comparison across all families
   * @param family - The material family to get properties for
   * @returns Array of all property keys sorted alphabetically
   */
  private async getAvailableProperties(family: MaterialFamily): Promise<string[]> {
    const metadata = await this.propertyMetadataService.getPropertyMetadataByFamily(family);
    const propertyKeys = Object.keys(metadata);
    return propertyKeys.sort((a, b) => a.localeCompare(b));
  }

  /**
   * Get all available properties for material comparison across multiple families
   * @param families - Array of material families to get properties for
   * @returns Array of all property keys sorted alphabetically
   */
  private async getAllAvailableProperties(families: MaterialFamily[]): Promise<string[]> {
    const allProperties = new Set<string>();

    for (const family of families) {
      const properties = await this.getAvailableProperties(family);
      for (const property of properties) {
        allProperties.add(property);
      }
    }

    return [...allProperties];
  }

  /**
   * Get all property metadata for material comparison across multiple families
   * @param families - Array of material families to get metadata for
   * @returns Object containing property metadata for the specified families
   */
  private async getAllPropertyMetadata(families: MaterialFamily[]): Promise<Record<string, any>> {
    return this.propertyMetadataService.getConsolidatedPropertyMetadata(families);
  }

  /**
   * Get filtered property metadata based on requested properties and families
   * @param families - Array of material families to filter by
   * @param includeProperties - Array of property keys to include
   * @returns Object containing filtered property metadata
   */
  private async getFilteredPropertyMetadata(families: MaterialFamily[], includeProperties: string[]): Promise<Record<string, any>> {
    const allMetadata = await this.propertyMetadataService.getConsolidatedPropertyMetadata(families);
    const filteredMetadata: Record<string, any> = {};

    for (const property of includeProperties) {
      if (allMetadata[property]) {
        filteredMetadata[property] = allMetadata[property];
      }
    }

    return filteredMetadata;
  }
}
