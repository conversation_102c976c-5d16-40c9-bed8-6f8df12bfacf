import { ForbiddenException, Injectable, NotFoundException } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { capitalizeWords, createPaginatedResponse } from "../common/utils";
import { UserRole } from "../role/role.types";
import { UserService } from "../user/user.service";
import { MaterialPageResponseDto, MaterialSearchRequestDto, MaterialSearchResponseDto, PaginatedMaterialPageResponseDto, PaginatedMaterialResponseDto, UpdateMaterialDto } from "./dto";
import { AdditiveFamilyDataDto, ElastomerFamilyDataDto, FillerFamilyDataDto, PolymerFamilyDataDto, RecyclePolymerFamilyDataDto } from "./dto/family-data";
import { MaterialSearchPageRequestDto } from "./dto/material-search-page-request.dto";
import { MaterialStatusChangedEvent } from "./events";
import { MaterialFilters, MaterialRepository, MaterialWithSupplier, UpdateMaterialData } from "./repositories/material.repository";
import { Additive, Elastomer, Filler, Material, MaterialFamily, MaterialStatus, Polymer, RecyclePolymer, Supplier } from "@/generated/prisma";

interface MaterialWithFamilyData extends Material {
  additive?: Additive | null
  elastomer?: Elastomer | null
  filler?: Filler | null
  polymer?: Polymer | null
  recyclePolymer?: RecyclePolymer | null
  supplier?: Supplier | null
}

@Injectable()
export class MaterialService {
  public constructor(
    private readonly materialRepository: MaterialRepository,
    private readonly eventEmitter: EventEmitter2,
    private readonly userService: UserService
  ) {}

  public async searchMaterials(searchRequest: MaterialSearchRequestDto & { userRole?: string }): Promise<PaginatedMaterialResponseDto> {
    const { page = 1, limit = 10, userRole, ...filters } = searchRequest;

    const materialFilters: MaterialFilters = {
      ...filters,
      limit,
      page,
    };

    if (userRole && userRole.toUpperCase() === (UserRole.ENGINEERS as string)) {
      materialFilters.status = MaterialStatus.AVAILABLE;
    }

    const result = await this.materialRepository.findAll(materialFilters);

    const materialResponses = result.data.map(material => this.mapToMaterialSearchResponse(material));

    return createPaginatedResponse({
      data: materialResponses,
      limit,
      page,
      total: result.total,
    });
  }

  public async getMaterialOrigins(): Promise<string[]> {
    const result = await this.materialRepository.findDistinctOrigins();

    const origins = result
      .filter(item => item.origin && item.origin.trim() !== "")
      .map(item => item.origin.trim().toLowerCase());

    const uniqueOrigins = [...new Set(origins)].map(origin => capitalizeWords(origin));

    return uniqueOrigins;
  }

  public async searchMaterialsWithCriteria(
    searchRequest: MaterialSearchPageRequestDto & { userRole?: string },
  ): Promise<PaginatedMaterialPageResponseDto> {
    const { page = 1, limit = 10, userRole, family, familyCriteria, ...filters } = searchRequest;

    const materialFilters: MaterialFilters & { familyFilters?: typeof familyCriteria } = {
      ...filters,
      familyFilters: familyCriteria,
      limit,
      page,
      userRole,
    };

    if (userRole === UserRole.FEEDSTOCK_RECYCLING_MEMBERS) {
      if (family && family !== MaterialFamily.RECYCLE_POLYMERS) {
        throw new ForbiddenException("Feedstock recycling members can only access materials from the RECYCLE_POLYMERS family.");
      }
      materialFilters.family = MaterialFamily.RECYCLE_POLYMERS;
    }
    else if (family) {
      if (family === MaterialFamily.RECYCLE_POLYMERS) {
        throw new ForbiddenException("Access to RECYCLE_POLYMERS family is not allowed.");
      }
      materialFilters.family = family;
    }

    const result = await this.materialRepository.findWithFamilyFieldFilters(materialFilters);

    const materialResponses = result.data.map(material => this.mapToMaterialPageResponse(material));

    return createPaginatedResponse({
      data: materialResponses,
      limit,
      page,
      total: result.total,
    });
  }

  public async updateMaterial(id: string, updateMaterialDto: UpdateMaterialDto, userRole?: UserRole): Promise<MaterialSearchResponseDto> {
    const existingMaterial = await this.materialRepository.findById(id);
    if (!existingMaterial) {
      throw new NotFoundException(`Material with ID ${id} not found`);
    }

    this.validateUserRoleForMaterialUpdate(userRole, existingMaterial, updateMaterialDto);

    const finalUpdateData: UpdateMaterialData = {
      ...updateMaterialDto,
      family: existingMaterial.family,
    };

    const updatedMaterial = await this.materialRepository.update(id, finalUpdateData);

    await this.handleMaterialStatusChange(existingMaterial.status, updatedMaterial.status);

    return this.mapToMaterialSearchResponse(updatedMaterial);
  }

  private validateUserRoleForMaterialUpdate(
    userRole: UserRole | undefined,
    existingMaterial: MaterialWithSupplier,
    updateMaterialDto: UpdateMaterialDto,
  ): void {
    if (userRole === UserRole.FEEDSTOCK_RECYCLING_MEMBERS) {
      if (existingMaterial.family !== MaterialFamily.RECYCLE_POLYMERS) {
        throw new ForbiddenException("Feedstock role can only update materials with family RECYCLE_POLYMERS");
      }
    }
    else if (existingMaterial.family === MaterialFamily.RECYCLE_POLYMERS || updateMaterialDto.family === MaterialFamily.RECYCLE_POLYMERS) {
      throw new ForbiddenException("Only feedstock role can update materials with family RECYCLE_POLYMERS");
    }
  }

  private async handleMaterialStatusChange(oldStatus: MaterialStatus, newStatus: MaterialStatus): Promise<void> {
    if (oldStatus !== newStatus) {
      const userIds = await this.getUsersForStatusChange(newStatus);

      this.eventEmitter.emit(
        "material.status-changed",
        new MaterialStatusChangedEvent(
          newStatus,
          userIds
        )
      );
    }
  }

  private async getUsersForStatusChange(status: MaterialStatus): Promise<string[]> {
    if (status === MaterialStatus.UNDER_REVIEW) {
      const feedstockUsers = await this.userService.findAll({ roleCode: UserRole.FEEDSTOCK_RECYCLING_MEMBERS });
      return feedstockUsers.data.map(user => user.id);
    }

    const allUsers = await this.userService.findAll({});
    return allUsers.data.map(user => user.id);
  }

  private mapToMaterialSearchResponse(material: MaterialWithSupplier): MaterialSearchResponseDto {
    return {
      family: material.family,
      id: material.id,
      origin: (material.origin ?? undefined) as string | null,
      reference: material.reference,
      status: material.status,
      supplier: (material.supplier?.name ?? undefined) as string | null,
      supplierBatchNumber: (material.supplierBatchNumber ?? undefined) as string | null,
      type: material.type,
    };
  }

  private mapToMaterialPageResponse(material: MaterialWithFamilyData): MaterialPageResponseDto {
    const baseResponse = this.mapToMaterialSearchResponse(material);

    let familyData: PolymerFamilyDataDto | FillerFamilyDataDto | ElastomerFamilyDataDto | AdditiveFamilyDataDto | RecyclePolymerFamilyDataDto | undefined;
    switch (material.family) {
      case MaterialFamily.POLYMERS: {
        familyData = material.polymer as PolymerFamilyDataDto | undefined;
        break;
      }
      case MaterialFamily.FILLERS: {
        familyData = material.filler as FillerFamilyDataDto | undefined;
        break;
      }
      case MaterialFamily.ELASTOMERS: {
        familyData = material.elastomer as ElastomerFamilyDataDto | undefined;
        break;
      }
      case MaterialFamily.ADDITIVES: {
        familyData = material.additive as AdditiveFamilyDataDto | undefined;
        break;
      }
      case MaterialFamily.RECYCLE_POLYMERS: {
        familyData = material.recyclePolymer as RecyclePolymerFamilyDataDto | undefined;
        break;
      }
      default: {
        familyData = undefined;
        break;
      }
    }

    return {
      ...baseResponse,
      familyData,
    };
  }
}
