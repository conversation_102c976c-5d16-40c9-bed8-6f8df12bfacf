import { filterConditionBuilder } from "./filter-condition.builder";

import { CriteriaOperator } from "@/common/dto/criteria-operator.dto";

describe("filterConditionBuilder", () => {
  describe("buildCondition", () => {
    it("should build greater than or equal condition", () => {
      const criterion = {
        operator: CriteriaOperator.GREATER_THAN_OR_EQUAL,
        propertyName: "mfiAv",
        value: 10,
      };

      const result = filterConditionBuilder.buildCondition(criterion);

      expect(result).toEqual({ gte: 10 });
    });

    it("should build greater than condition", () => {
      const criterion = {
        operator: CriteriaOperator.GREATER_THAN,
        propertyName: "mfiAv",
        value: 10,
      };

      const result = filterConditionBuilder.buildCondition(criterion);

      expect(result).toEqual({ gt: 10 });
    });

    it("should build less than or equal condition", () => {
      const criterion = {
        operator: CriteriaOperator.LESS_THAN_OR_EQUAL,
        propertyName: "mfiAv",
        value: 10,
      };

      const result = filterConditionBuilder.buildCondition(criterion);

      expect(result).toEqual({ lte: 10 });
    });

    it("should build less than condition", () => {
      const criterion = {
        operator: CriteriaOperator.LESS_THAN,
        propertyName: "mfiAv",
        value: 10,
      };

      const result = filterConditionBuilder.buildCondition(criterion);

      expect(result).toEqual({ lt: 10 });
    });

    it("should build equals condition", () => {
      const criterion = {
        operator: CriteriaOperator.EQUAL,
        propertyName: "mfiAv",
        value: 10,
      };

      const result = filterConditionBuilder.buildCondition(criterion);

      expect(result).toEqual({ equals: 10 });
    });

    it("should build between condition", () => {
      const criterion = {
        maxValue: 15,
        minValue: 5,
        operator: CriteriaOperator.BETWEEN,
        propertyName: "mfiAv",
      };

      const result = filterConditionBuilder.buildCondition(criterion);

      expect(result).toEqual({ gte: 5, lte: 15 });
    });

    it("should handle string values", () => {
      const criterion = {
        operator: CriteriaOperator.EQUAL,
        propertyName: "codeanonymFiller",
        value: "CC-001",
      };

      const result = filterConditionBuilder.buildCondition(criterion);

      expect(result).toEqual({ equals: "CC-001" });
    });

    it("should handle number values", () => {
      const criterion = {
        operator: CriteriaOperator.GREATER_THAN,
        propertyName: "bet",
        value: 15.5,
      };

      const result = filterConditionBuilder.buildCondition(criterion);

      expect(result).toEqual({ gt: 15.5 });
    });

    it("should return undefined for unknown operator", () => {
      const criterion = {
        operator: "unknown" as CriteriaOperator,
        propertyName: "mfiAv",
        value: 10,
      };

      const result = filterConditionBuilder.buildCondition(criterion);

      expect(result).toBeUndefined();
    });
  });

  describe("buildGenericFilters", () => {
    it("should build filters with single criterion", () => {
      const criteria = [
        {
          operator: CriteriaOperator.GREATER_THAN_OR_EQUAL,
          propertyName: "mfiAv",
          value: 10,
        },
      ];

      const result = filterConditionBuilder.buildGenericFilters(criteria);

      expect(result).toEqual({
        ["AND"]: [
          {
            mfiAv: { gte: 10 },
          },
        ],
      });
    });

    it("should build filters with multiple criteria", () => {
      const criteria = [
        {
          operator: CriteriaOperator.GREATER_THAN_OR_EQUAL,
          propertyName: "mfiAv",
          value: 10,
        },
        {
          operator: CriteriaOperator.LESS_THAN,
          propertyName: "densityAv",
          value: 1.5,
        },
        {
          maxValue: 5000,
          minValue: 2000,
          operator: CriteriaOperator.BETWEEN,
          propertyName: "tensileModulusAv",
        },
      ];

      const result = filterConditionBuilder.buildGenericFilters(criteria);

      expect(result).toEqual({
        ["AND"]: [
          {
            mfiAv: { gte: 10 },
          },
          {
            densityAv: { lt: 1.5 },
          },
          {
            tensileModulusAv: { gte: 2000, lte: 5000 },
          },
        ],
      });
    });

    it("should filter out criteria with unknown operators", () => {
      const criteria = [
        {
          operator: CriteriaOperator.GREATER_THAN,
          propertyName: "mfiAv",
          value: 10,
        },
        {
          operator: "unknown" as CriteriaOperator,
          propertyName: "densityAv",
          value: 1.5,
        },
      ];

      const result = filterConditionBuilder.buildGenericFilters(criteria);

      expect(result).toEqual({
        ["AND"]: [
          {
            mfiAv: { gt: 10 },
          },
        ],
      });
    });

    it("should return empty object for empty criteria", () => {
      const criteria: never[] = [];

      const result = filterConditionBuilder.buildGenericFilters(criteria);

      expect(result).toEqual({});
    });

    it("should return empty object when all criteria have invalid operators", () => {
      const criteria = [
        {
          operator: "invalid" as CriteriaOperator,
          propertyName: "mfiAv",
          value: 10,
        },
        {
          operator: "unknown" as CriteriaOperator,
          propertyName: "densityAv",
          value: 1.5,
        },
      ];

      const result = filterConditionBuilder.buildGenericFilters(criteria);

      expect(result).toEqual({});
    });
  });
});
