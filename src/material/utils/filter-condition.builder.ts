import { CriteriaOperator } from "@/common/dto/criteria-operator.dto";

interface FilterCriterion {
  operator: CriteriaOperator
  propertyName: string
  value?: unknown
  minValue?: unknown
  maxValue?: unknown
}

type FilterCondition = Record<string, unknown>;

interface FilterResult {
  ["AND"]?: Record<string, FilterCondition>[]
}

export const filterConditionBuilder = {
  buildCondition(criterion: FilterCriterion): FilterCondition | undefined {
    const { operator, value, minValue, maxValue } = criterion;

    switch (operator) {
      case CriteriaOperator.GREATER_THAN_OR_EQUAL: {
        return { gte: value };
      }
      case CriteriaOperator.GREATER_THAN: {
        return { gt: value };
      }
      case CriteriaOperator.LESS_THAN_OR_EQUAL: {
        return { lte: value };
      }
      case CriteriaOperator.LESS_THAN: {
        return { lt: value };
      }
      case CriteriaOperator.EQUAL: {
        return { equals: value };
      }
      case CriteriaOperator.BETWEEN: {
        return { gte: minValue, lte: maxValue };
      }
      default: {
        return undefined;
      }
    }
  },

  buildGenericFilters(criteria: FilterCriterion[]): FilterResult {
    const andConditions: Record<string, FilterCondition>[] = [];

    for (const criterion of criteria) {
      const condition = filterConditionBuilder.buildCondition(criterion);
      if (condition) {
        andConditions.push({ [criterion.propertyName]: condition });
      }
    }

    return andConditions.length > 0 ? { ["AND"]: andConditions } : {};
  },
};
