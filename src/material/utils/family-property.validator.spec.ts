import { BadRequestException } from "@nestjs/common";

import { propertyValidator } from "./family-property.validator";

import { MaterialFamily } from "@/generated/prisma";

describe("propertyValidator", () => {
  describe("getFamilyProperties", () => {
    it("should return valid properties for POLYMERS family", () => {
      const properties = propertyValidator.getFamilyProperties(MaterialFamily.POLYMERS);

      expect(properties).toContain("mfiAv");
      expect(properties).toContain("densityAv");
      expect(properties).toContain("tensileModulusAv");
      expect(properties).toContain("flexuralModulusAv");
      expect(properties).toContain("hdtBAv");
      expect(properties.length).toBeGreaterThan(40);
    });

    it("should return valid properties for FILLERS family", () => {
      const properties = propertyValidator.getFamilyProperties(MaterialFamily.FILLERS);

      expect(properties).toContain("bet");
      expect(properties).toContain("d50");
      expect(properties).toContain("d95d05");
      expect(properties).toContain("whiteness");
      expect(properties).toContain("form");
      expect(properties).toContain("codeanonymFiller");
    });

    it("should return valid properties for ELASTOMERS family", () => {
      const properties = propertyValidator.getFamilyProperties(MaterialFamily.ELASTOMERS);

      expect(properties).toContain("density");
      expect(properties).toContain("mfi190216");
      expect(properties).toContain("flexModulus");
      expect(properties).toContain("shoreA");
      expect(properties).toContain("shoreD");
      expect(properties).toContain("hdtB");
    });

    it("should return valid properties for ADDITIVES family", () => {
      const properties = propertyValidator.getFamilyProperties(MaterialFamily.ADDITIVES);

      expect(properties).toContain("anonymizationCode");
    });

    it("should return valid properties for RECYCLE_POLYMERS family", () => {
      const properties = propertyValidator.getFamilyProperties(MaterialFamily.RECYCLE_POLYMERS);

      expect(properties).toContain("color");
      expect(properties).toContain("pirPcrElv");
      expect(properties).toContain("wasteDetails");
      expect(properties).toContain("materialForm");
      expect(properties).toContain("mfiAv");
      expect(properties).toContain("densityAv");
      expect(properties).toContain("priceExw");
    });
  });

  describe("getFamilyProperties", () => {
    it("should return empty array for unknown family", () => {
      const unknownFamily = "UNKNOWN_FAMILY" as MaterialFamily;
      const properties = propertyValidator.getFamilyProperties(unknownFamily);

      expect(properties).toEqual([]);
    });

    it("should return specific properties for each family", () => {
      const polymerProperties = propertyValidator.getFamilyProperties(MaterialFamily.POLYMERS);
      const fillerProperties = propertyValidator.getFamilyProperties(MaterialFamily.FILLERS);

      expect(polymerProperties).not.toEqual(fillerProperties);
      expect(polymerProperties).toContain("mfiAv");
      expect(fillerProperties).toContain("bet");
      expect(fillerProperties).not.toContain("mfiAv");
    });
  });

  describe("getPropertyType", () => {
    it("should return correct type for valid property", () => {
      const type = propertyValidator.getPropertyType(MaterialFamily.POLYMERS, "mfiAv");
      expect(type).toBe("number");
    });

    it("should return correct type for string property", () => {
      const type = propertyValidator.getPropertyType(MaterialFamily.POLYMERS, "validationEngineering");
      expect(type).toBe("string");
    });

    it("should return correct type for date property", () => {
      const type = propertyValidator.getPropertyType(MaterialFamily.POLYMERS, "resultUpdateDate");
      expect(type).toBe("date");
    });

    it("should return undefined for invalid property", () => {
      const type = propertyValidator.getPropertyType(MaterialFamily.POLYMERS, "invalidProperty");
      expect(type).toBeUndefined();
    });
  });

  describe("validateCriteria", () => {
    it("should validate criteria with valid properties", () => {
      const criteria = [
        { operator: ">=", propertyName: "mfiAv", value: 10 },
        { operator: "<", propertyName: "densityAv", value: 1.5 },
      ];

      expect(() => {
        propertyValidator.validateCriteria(MaterialFamily.POLYMERS, criteria);
      }).not.toThrow();
    });

    it("should validate criteria for different families", () => {
      const fillerCriteria = [
        { operator: ">=", propertyName: "bet", value: 10 },
        { operator: "contains", propertyName: "form", value: "powder" },
      ];

      expect(() => {
        propertyValidator.validateCriteria(MaterialFamily.FILLERS, fillerCriteria);
      }).not.toThrow();
    });

    it("should throw error for criteria with invalid property", () => {
      const criteria = [
        { operator: ">=", propertyName: "mfiAv", value: 10 },
        { operator: "<", propertyName: "invalidProperty", value: 1.5 },
      ];

      expect(() => {
        propertyValidator.validateCriteria(MaterialFamily.POLYMERS, criteria);
      }).toThrow(BadRequestException);
    });

    it("should throw error for mixing family properties", () => {
      const criteria = [
        { operator: ">=", propertyName: "mfiAv", value: 10 },
        { operator: "<", propertyName: "bet", value: 1.5 },
      ];

      expect(() => {
        propertyValidator.validateCriteria(MaterialFamily.POLYMERS, criteria);
      }).toThrow(BadRequestException);
    });

    it("should handle empty criteria array", () => {
      expect(() => {
        propertyValidator.validateCriteria(MaterialFamily.POLYMERS, []);
      }).not.toThrow();
    });

    it("should handle criteria without propertyName", () => {
      const criteria = [
        { operator: ">=", value: 10 },
        { operator: "<", propertyName: "mfiAv", value: 1.5 },
      ];

      expect(() => {
        propertyValidator.validateCriteria(MaterialFamily.POLYMERS, criteria);
      }).not.toThrow();
    });

    it("should validate all criteria even if one is invalid", () => {
      const criteria = [
        { operator: ">=", propertyName: "mfiAv", value: 10 },
        { operator: "<", propertyName: "invalidProperty1", value: 1.5 },
        { operator: "=", propertyName: "invalidProperty2", value: 2 },
      ];

      expect(() => {
        propertyValidator.validateCriteria(MaterialFamily.POLYMERS, criteria);
      }).toThrow(BadRequestException);
    });

    it("should validate complex criteria for recycle polymers", () => {
      const criteria = [
        { operator: "contains", propertyName: "color", value: "Natural" },
        { operator: "=", propertyName: "pirPcrElv", value: "PCR" },
        { maxValue: 15, minValue: 5, operator: "between", propertyName: "mfiAv" },
        { operator: "<=", propertyName: "priceExw", value: 1000 },
      ];

      expect(() => {
        propertyValidator.validateCriteria(MaterialFamily.RECYCLE_POLYMERS, criteria);
      }).not.toThrow();
    });

    it("should validate elastomer-specific properties", () => {
      const criteria = [
        { operator: ">=", propertyName: "shoreA", value: 50 },
        { operator: "<=", propertyName: "shoreD", value: 80 },
        { maxValue: 10, minValue: 1, operator: "between", propertyName: "mfi190216" },
      ];

      expect(() => {
        propertyValidator.validateCriteria(MaterialFamily.ELASTOMERS, criteria);
      }).not.toThrow();
    });

    it("should validate additive-specific properties", () => {
      const criteria = [
        { operator: "contains", propertyName: "anonymizationCode", value: "ADD" },
      ];

      expect(() => {
        propertyValidator.validateCriteria(MaterialFamily.ADDITIVES, criteria);
      }).not.toThrow();
    });
  });
});
