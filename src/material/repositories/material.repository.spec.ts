import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../../prisma.service";
import { MaterialFilters, MaterialRepository } from "./material.repository";
import { CriteriaOperator } from "@/common/dto/criteria-operator.dto";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

describe("MaterialRepository", (): void => {
  let repository: MaterialRepository;

  const expectFindManyCalledWith = (mockFindMany: jest.Mock, expectedCall: unknown): void => {
    expect(mockFindMany).toHaveBeenCalledWith(expectedCall);
  };

  const expectCountCalledWith = (mockCount: jest.Mock, expectedCall: unknown): void => {
    expect(mockCount).toHaveBeenCalledWith(expectedCall);
  };

  const expectFindUniqueCalledWith = (mockFindUnique: jest.Mo<PERSON>, expectedCall: unknown): void => {
    expect(mockFindUnique).toHaveBeenCalledWith(expectedCall);
  };

  const expectUpdateCalledWith = (mockUpdate: jest.Mock, expectedCall: unknown): void => {
    expect(mockUpdate).toHaveBeenCalledWith(expectedCall);
  };

  const mockMaterial = {
    family: MaterialFamily.POLYMERS,
    id: "material-123",
    origin: "Europe",
    polymer: {
      density: 1.2,
      glassTempRange: "80-120",
      id: "polymer-123",
      meltingPoint: 180,
    },
    reference: "MAT-001",
    status: MaterialStatus.AVAILABLE,
    supplier: {
      id: "supplier-123",
      name: "Test Supplier",
    },
    supplierBatchNumber: "BATCH-123",
    supplierId: "supplier-123",
    type: "Polypropylene",
  };

  const mockPrismaMaterial = {
    count: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
  };

  beforeEach(async (): Promise<void> => {
    jest.clearAllMocks();

    const mockPrismaService = {
      material: mockPrismaMaterial,
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MaterialRepository,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    repository = module.get<MaterialRepository>(MaterialRepository);
  });

  describe("findAll", (): void => {
    it("should return paginated materials with basic filters", async function (this: void): Promise<void> {
      const filters: MaterialFilters = {
        limit: 10,
        page: 1,
      };

      const mockData = [mockMaterial];
      const mockTotal = 1;

      mockPrismaMaterial.findMany.mockResolvedValue(mockData);
      mockPrismaMaterial.count.mockResolvedValue(mockTotal);

      const result = await repository.findAll(filters);

      const expectedFindManyCall = {
        include: {
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: 0,
        take: 10,
        where: {},
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedFindManyCall);

      const expectedCountCall = { where: {} };
      expectCountCalledWith(mockPrismaMaterial.count, expectedCountCall);

      expect(result).toEqual({
        data: mockData,
        limit: 10,
        page: 1,
        total: mockTotal,
      });
    });

    it("should apply search filter with OR conditions", async function (this: void): Promise<void> {
      const filters: MaterialFilters = {
        limit: 10,
        page: 1,
        search: "polymer",
      };

      mockPrismaMaterial.findMany.mockResolvedValue([mockMaterial]);
      mockPrismaMaterial.count.mockResolvedValue(1);

      await repository.findAll(filters);

      const orConditions = [
        { origin: { contains: "polymer", mode: "insensitive" } },
        { reference: { contains: "polymer", mode: "insensitive" } },
        { supplier: { name: { contains: "polymer", mode: "insensitive" } } },
        { supplierBatchNumber: { contains: "polymer", mode: "insensitive" } },
        { type: { contains: "polymer", mode: "insensitive" } },
      ];

      const expectedCall = {
        include: {
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: 0,
        take: 10,
        where: {
          ["OR"]: orConditions,
        },
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedCall);
    });

    it("should apply origin filter", async function (this: void): Promise<void> {
      const filters: MaterialFilters = {
        limit: 10,
        origin: "Europe",
        page: 1,
      };

      mockPrismaMaterial.findMany.mockResolvedValue([mockMaterial]);
      mockPrismaMaterial.count.mockResolvedValue(1);

      await repository.findAll(filters);

      const expectedCall = {
        include: {
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: 0,
        take: 10,
        where: {
          origin: { contains: "Europe", mode: "insensitive" },
        },
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedCall);
    });

    it("should apply family filter", async function (this: void): Promise<void> {
      const filters: MaterialFilters = {
        family: MaterialFamily.POLYMERS,
        limit: 10,
        page: 1,
      };

      mockPrismaMaterial.findMany.mockResolvedValue([mockMaterial]);
      mockPrismaMaterial.count.mockResolvedValue(1);

      await repository.findAll(filters);

      const expectedCall = {
        include: {
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: 0,
        take: 10,
        where: {
          family: MaterialFamily.POLYMERS,
        },
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedCall);
    });

    it("should apply status filter", async function (this: void): Promise<void> {
      const filters: MaterialFilters = {
        limit: 10,
        page: 1,
        status: MaterialStatus.AVAILABLE,
      };

      mockPrismaMaterial.findMany.mockResolvedValue([mockMaterial]);
      mockPrismaMaterial.count.mockResolvedValue(1);

      await repository.findAll(filters);

      const expectedCall = {
        include: {
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: 0,
        take: 10,
        where: {
          status: MaterialStatus.AVAILABLE,
        },
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedCall);
    });

    it("should apply supplierId filter", async function (this: void): Promise<void> {
      const filters: MaterialFilters = {
        limit: 10,
        page: 1,
        supplierId: "supplier-123",
      };

      mockPrismaMaterial.findMany.mockResolvedValue([mockMaterial]);
      mockPrismaMaterial.count.mockResolvedValue(1);

      await repository.findAll(filters);

      const expectedCall = {
        include: {
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: 0,
        take: 10,
        where: {
          supplierId: "supplier-123",
        },
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedCall);
    });

    it("should apply supplier name filter", async function (this: void): Promise<void> {
      const filters: MaterialFilters = {
        limit: 10,
        page: 1,
        supplier: "Test Supplier",
      };

      mockPrismaMaterial.findMany.mockResolvedValue([mockMaterial]);
      mockPrismaMaterial.count.mockResolvedValue(1);

      await repository.findAll(filters);

      const expectedCall = {
        include: {
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: 0,
        take: 10,
        where: {
          supplier: { name: { contains: "Test Supplier", mode: "insensitive" } },
        },
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedCall);
    });

    it("should apply supplierBatchNumber filter", async function (this: void): Promise<void> {
      const filters: MaterialFilters = {
        limit: 10,
        page: 1,
        supplierBatchNumber: "BATCH-123",
      };

      mockPrismaMaterial.findMany.mockResolvedValue([mockMaterial]);
      mockPrismaMaterial.count.mockResolvedValue(1);

      await repository.findAll(filters);

      const expectedCall = {
        include: {
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: 0,
        take: 10,
        where: {
          supplierBatchNumber: { contains: "BATCH-123", mode: "insensitive" },
        },
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedCall);
    });

    it("should apply reference filter", async function (this: void): Promise<void> {
      const filters: MaterialFilters = {
        limit: 10,
        page: 1,
        reference: "MAT-001",
      };

      mockPrismaMaterial.findMany.mockResolvedValue([mockMaterial]);
      mockPrismaMaterial.count.mockResolvedValue(1);

      await repository.findAll(filters);

      const expectedCall = {
        include: {
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: 0,
        take: 10,
        where: {
          reference: { contains: "MAT-001", mode: "insensitive" },
        },
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedCall);
    });

    it("should apply multiple filters together", async function (this: void): Promise<void> {
      const filters: MaterialFilters = {
        family: MaterialFamily.POLYMERS,
        limit: 10,
        origin: "Europe",
        page: 1,
        search: "polymer",
        status: MaterialStatus.AVAILABLE,
        supplierId: "supplier-123",
      };

      mockPrismaMaterial.findMany.mockResolvedValue([mockMaterial]);
      mockPrismaMaterial.count.mockResolvedValue(1);

      await repository.findAll(filters);

      const orConditions = [
        { origin: { contains: "polymer", mode: "insensitive" } },
        { reference: { contains: "polymer", mode: "insensitive" } },
        { supplier: { name: { contains: "polymer", mode: "insensitive" } } },
        { supplierBatchNumber: { contains: "polymer", mode: "insensitive" } },
        { type: { contains: "polymer", mode: "insensitive" } },
      ];

      const expectedCall = {
        include: {
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: 0,
        take: 10,
        where: {
          ["OR"]: orConditions,
          family: MaterialFamily.POLYMERS,
          origin: { contains: "Europe", mode: "insensitive" },
          status: MaterialStatus.AVAILABLE,
          supplierId: "supplier-123",
        },
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedCall);
    });

    it("should calculate pagination offset correctly", async function (this: void): Promise<void> {
      const filters: MaterialFilters = {
        limit: 5,
        page: 3,
      };

      mockPrismaMaterial.findMany.mockResolvedValue([]);
      mockPrismaMaterial.count.mockResolvedValue(0);

      await repository.findAll(filters);

      const expectedCall = {
        include: {
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: 10,
        take: 5,
        where: {},
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedCall);
    });

    it("should use default pagination values when not provided", async function (this: void): Promise<void> {
      const filters: MaterialFilters = {};

      mockPrismaMaterial.findMany.mockResolvedValue([]);
      mockPrismaMaterial.count.mockResolvedValue(0);

      const result = await repository.findAll(filters);

      const expectedCall = {
        include: {
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: 0,
        take: 10,
        where: {},
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedCall);

      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
    });
  });

  describe("findDistinctOrigins", (): void => {
    it("should return distinct origins ordered by origin", async function (this: void): Promise<void> {
      const mockOrigins = [
        { origin: "Asia" },
        { origin: "Europe" },
        { origin: "North America" },
      ];

      mockPrismaMaterial.findMany.mockResolvedValue(mockOrigins);

      const result = await repository.findDistinctOrigins();

      const expectedCall = {
        distinct: ["origin"],
        orderBy: {
          origin: "asc",
        },
        select: {
          origin: true,
        },
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedCall);

      expect(result).toEqual(mockOrigins);
    });

    it("should handle empty origins result", async function (this: void): Promise<void> {
      mockPrismaMaterial.findMany.mockResolvedValue([]);

      const result = await repository.findDistinctOrigins();

      const expectedCall = {
        distinct: ["origin"],
        orderBy: {
          origin: "asc",
        },
        select: {
          origin: true,
        },
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectedCall);

      expect(result).toEqual([]);
    });
  });

  describe("findById", (): void => {
    it("should return material by id with supplier", async function (this: void): Promise<void> {
      const materialId = "material-123";
      mockPrismaMaterial.findUnique.mockResolvedValue(mockMaterial);

      const result = await repository.findById(materialId);

      const expectedCall = {
        include: {
          supplier: true,
        },
        where: { id: materialId },
      };
      expectFindUniqueCalledWith(mockPrismaMaterial.findUnique, expectedCall);

      expect(result).toEqual(mockMaterial);
    });

    it("should return null when material not found", async function (this: void): Promise<void> {
      const materialId = "non-existent-id";
      mockPrismaMaterial.findUnique.mockResolvedValueOnce(undefined as never);

      const result = await repository.findById(materialId);

      const expectedCall = {
        include: {
          supplier: true,
        },
        where: { id: materialId },
      };
      expectFindUniqueCalledWith(mockPrismaMaterial.findUnique, expectedCall);

      expect(result).toBeUndefined();
    });
  });

  describe("update", (): void => {
    it("should update material and return updated data with supplier", async function (this: void): Promise<void> {
      const materialId = "material-123";
      const updateData = {
        origin: "Updated Origin",
        status: MaterialStatus.ARCHIVE,
        type: "Updated Polypropylene",
      };

      const updatedMaterial = {
        ...mockMaterial,
        ...updateData,
      };

      mockPrismaMaterial.update.mockResolvedValue(updatedMaterial);

      const result = await repository.update(materialId, updateData);

      const expectedCall = {
        data: updateData,
        include: {
          additive: true,
          elastomer: true,
          filler: true,
          polymer: true,
          recyclePolymer: true,
          supplier: true,
        },
        where: { id: materialId },
      };
      expectUpdateCalledWith(mockPrismaMaterial.update, expectedCall);

      expect(result).toEqual(updatedMaterial);
    });

    it("should update material with partial data", async function (this: void): Promise<void> {
      const materialId = "material-123";
      const updateData = {
        status: MaterialStatus.ARCHIVE,
      };

      const updatedMaterial = {
        ...mockMaterial,
        status: MaterialStatus.ARCHIVE,
      };

      mockPrismaMaterial.update.mockResolvedValue(updatedMaterial);

      const result = await repository.update(materialId, updateData);

      const expectedCall = {
        data: updateData,
        include: {
          additive: true,
          elastomer: true,
          filler: true,
          polymer: true,
          recyclePolymer: true,
          supplier: true,
        },
        where: { id: materialId },
      };
      expectUpdateCalledWith(mockPrismaMaterial.update, expectedCall);

      expect(result).toEqual(updatedMaterial);
    });

    it("should update material with all fields", async function (this: void): Promise<void> {
      const materialId = "material-123";
      const updateData = {
        family: MaterialFamily.ELASTOMERS,
        origin: "South America",
        reference: "NEW-REF-001",
        status: MaterialStatus.UNDER_REVIEW,
        supplierBatchNumber: "NEW-BATCH-789",
        supplierId: "new-supplier-id",
        type: "New Material Type",
      };

      const updatedMaterial = {
        ...mockMaterial,
        ...updateData,
        supplier: {
          id: "new-supplier-id",
          name: "New Supplier",
        },
      };

      mockPrismaMaterial.update.mockResolvedValue(updatedMaterial);

      const result = await repository.update(materialId, updateData);

      const expectedCall = {
        data: updateData,
        include: {
          additive: true,
          elastomer: true,
          filler: true,
          polymer: true,
          recyclePolymer: true,
          supplier: true,
        },
        where: { id: materialId },
      };
      expectUpdateCalledWith(mockPrismaMaterial.update, expectedCall);

      expect(result).toEqual(updatedMaterial);
    });
  });

  describe("findWithFamilyFieldFilters", () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("should find materials with family field filters", async function (this: void) {
      const mockFilters = {
        family: MaterialFamily.POLYMERS,
        familyFilters: [
          {
            operator: CriteriaOperator.GREATER_THAN_OR_EQUAL,
            propertyName: "mfiAv",
            value: 10,
          },
        ],
        limit: 10,
        page: 1,
      };

      const mockMaterials = [mockMaterial];
      mockPrismaMaterial.findMany.mockResolvedValue(mockMaterials);
      mockPrismaMaterial.count.mockResolvedValue(1);

      const result = await repository.findWithFamilyFieldFilters(mockFilters);

      const expectationObject = {
        include: {
          additive: true,
          elastomer: true,
          filler: true,
          polymer: true,
          recyclePolymer: true,
          supplier: true,
        },
        orderBy: [
          { family: "asc" },
          { reference: "asc" },
        ],
        skip: 0,
        take: 10,
        where: expect.objectContaining({
          family: MaterialFamily.POLYMERS,
        }) as unknown,
      };
      expectFindManyCalledWith(mockPrismaMaterial.findMany, expectationObject);

      expect(result).toEqual({
        data: mockMaterials,
        limit: 10,
        page: 1,
        total: 1,
      });
    });

    it("should handle array filters for origin and status", async function (this: void): Promise<void> {
      const mockFilters = {
        limit: 10,
        origin: ["Europe", "Asia"],
        page: 1,
        status: [MaterialStatus.AVAILABLE, MaterialStatus.ARCHIVE],
      };

      const mockMaterials = [mockMaterial];
      mockPrismaMaterial.findMany.mockResolvedValue(mockMaterials);
      mockPrismaMaterial.count.mockResolvedValue(1);

      const result = await repository.findWithFamilyFieldFilters(mockFilters);

      expectFindManyCalledWith(mockPrismaMaterial.findMany, {
        include: expect.any(Object) as unknown,
        orderBy: expect.any(Array) as unknown,
        skip: 0,
        take: 10,
        where: expect.objectContaining({
          origin: { in: ["Europe", "Asia"] },
          status: { in: [MaterialStatus.AVAILABLE, MaterialStatus.ARCHIVE] },
        }) as unknown,
      });

      expect(result.data).toEqual(mockMaterials);
    });

    it("should apply user role filters for FEEDSTOCK_RECYCLING_MEMBERS", async function (this: void): Promise<void> {
      const mockFilters = {
        limit: 10,
        page: 1,
        userRole: "FEEDSTOCK_RECYCLING_MEMBERS",
      };

      mockPrismaMaterial.findMany.mockResolvedValue([]);
      mockPrismaMaterial.count.mockResolvedValue(0);

      await repository.findWithFamilyFieldFilters(mockFilters);

      expectFindManyCalledWith(mockPrismaMaterial.findMany, {
        include: expect.any(Object) as unknown,
        orderBy: expect.any(Array) as unknown,
        skip: 0,
        take: 10,
        where: expect.objectContaining({
          family: MaterialFamily.RECYCLE_POLYMERS,
        }) as unknown,
      });
    });

    it("should exclude recycle polymers for non-feedstock users when no family specified", async function (this: void) {
      const mockFilters = {
        limit: 10,
        page: 1,
        userRole: "ADMIN",
      };

      mockPrismaMaterial.findMany.mockResolvedValue([]);
      mockPrismaMaterial.count.mockResolvedValue(0);

      await repository.findWithFamilyFieldFilters(mockFilters);

      expectFindManyCalledWith(mockPrismaMaterial.findMany, {
        include: expect.any(Object) as unknown,
        orderBy: expect.any(Array) as unknown,
        skip: 0,
        take: 10,
        where: expect.objectContaining({
          family: { not: MaterialFamily.RECYCLE_POLYMERS },
        }) as unknown,
      });
    });

    it("should handle search with OR conditions", async function (this: void) {
      const mockFilters = {
        limit: 10,
        page: 1,
        search: "polymer",
      };

      mockPrismaMaterial.findMany.mockResolvedValue([]);
      mockPrismaMaterial.count.mockResolvedValue(0);

      await repository.findWithFamilyFieldFilters(mockFilters);

      expectFindManyCalledWith(mockPrismaMaterial.findMany, {
        include: expect.any(Object) as unknown,
        orderBy: expect.any(Array) as unknown,
        skip: 0,
        take: 10,
        where: expect.objectContaining({
          ["OR"]: [
            { origin: { contains: "polymer", mode: "insensitive" } },
            { reference: { contains: "polymer", mode: "insensitive" } },
            { supplier: { name: { contains: "polymer", mode: "insensitive" } } },
            { supplierBatchNumber: { contains: "polymer", mode: "insensitive" } },
            { type: { contains: "polymer", mode: "insensitive" } },
          ],
        }) as unknown,
      });
    });

    it("should handle pagination correctly", async function (this: void) {
      const mockFilters = {
        limit: 5,
        page: 3,
      };

      mockPrismaMaterial.findMany.mockResolvedValue([]);
      mockPrismaMaterial.count.mockResolvedValue(0);

      await repository.findWithFamilyFieldFilters(mockFilters);

      expectFindManyCalledWith(mockPrismaMaterial.findMany, {
        include: expect.any(Object) as unknown,
        orderBy: expect.any(Array) as unknown,
        skip: 10,
        take: 5,
        where: expect.any(Object) as unknown,
      });
    });
  });

  describe("buildWhereClause", () => {
    it("should handle single string origin filter", async function (this: void) {
      const mockFilters = {
        limit: 10,
        origin: "Europe",
        page: 1,
      };

      mockPrismaMaterial.findMany.mockResolvedValue([]);
      mockPrismaMaterial.count.mockResolvedValue(0);

      await repository.findAll(mockFilters);

      expectFindManyCalledWith(mockPrismaMaterial.findMany, {
        include: { supplier: true },
        orderBy: expect.any(Array) as unknown,
        skip: 0,
        take: 10,
        where: expect.objectContaining({
          origin: { contains: "Europe", mode: "insensitive" },
        }) as unknown,
      });
    });

    it("should handle single status filter", async function (this: void) {
      const mockFilters = {
        limit: 10,
        page: 1,
        status: MaterialStatus.AVAILABLE,
      };

      mockPrismaMaterial.findMany.mockResolvedValue([]);
      mockPrismaMaterial.count.mockResolvedValue(0);

      await repository.findAll(mockFilters);

      expectFindManyCalledWith(mockPrismaMaterial.findMany, {
        include: { supplier: true },
        orderBy: expect.any(Array) as unknown,
        skip: 0,
        take: 10,
        where: expect.objectContaining({
          status: MaterialStatus.AVAILABLE,
        }) as unknown,
      });
    });

    it("should handle supplier name filter", async function (this: void) {
      const mockFilters = {
        limit: 10,
        page: 1,
        supplier: "Test Supplier",
      };

      mockPrismaMaterial.findMany.mockResolvedValue([]);
      mockPrismaMaterial.count.mockResolvedValue(0);

      await repository.findAll(mockFilters);

      expectFindManyCalledWith(mockPrismaMaterial.findMany, {
        include: { supplier: true },
        orderBy: expect.any(Array) as unknown,
        skip: 0,
        take: 10,
        where: expect.objectContaining({
          supplier: { name: { contains: "Test Supplier", mode: "insensitive" } },
        }) as unknown,
      });
    });
  });
});
