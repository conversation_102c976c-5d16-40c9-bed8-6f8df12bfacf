import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../../prisma.service";
import { PropertyMetadataRepository } from "./property-metadata.repository";
import { MaterialFamily, PropertyType } from "@/generated/prisma";

describe("PropertyMetadataRepository", () => {
  let repository: PropertyMetadataRepository;

  const mockPrismaService = {
    propertyMetadata: {
      findMany: jest.fn(),
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PropertyMetadataRepository,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    repository = module.get<PropertyMetadataRepository>(PropertyMetadataRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(repository).toBeDefined();
  });

  describe("findByFamily", () => {
    it("should find property metadata by material family", async () => {
      const mockPropertyMetadata = [
        {
          id: "1",
          propertyKey: "mfiAv",
          description: "Melt Flow Index Average",
          unit: "g/10min",
          type: PropertyType.NUMBER,
          family: MaterialFamily.POLYMERS,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "2",
          propertyKey: "densityAv",
          description: "Density Average",
          unit: "g/cm³",
          type: PropertyType.NUMBER,
          family: MaterialFamily.POLYMERS,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockPrismaService.propertyMetadata.findMany.mockResolvedValue(mockPropertyMetadata);

      const result = await repository.findByFamily(MaterialFamily.POLYMERS);

      expect(mockPrismaService.propertyMetadata.findMany).toHaveBeenCalledWith({
        where: { family: MaterialFamily.POLYMERS },
        orderBy: { propertyKey: "asc" },
      });
      expect(result).toEqual(mockPropertyMetadata);
      expect(result).toHaveLength(2);
    });

    it("should return empty array when no metadata found for family", async () => {
      mockPrismaService.propertyMetadata.findMany.mockResolvedValue([]);

      const result = await repository.findByFamily(MaterialFamily.FILLERS);

      expect(mockPrismaService.propertyMetadata.findMany).toHaveBeenCalledWith({
        where: { family: MaterialFamily.FILLERS },
        orderBy: { propertyKey: "asc" },
      });
      expect(result).toEqual([]);
    });
  });

  describe("findByFamilies", () => {
    it("should find property metadata by multiple material families", async () => {
      const mockPropertyMetadata = [
        {
          id: "1",
          propertyKey: "mfiAv",
          description: "Melt Flow Index Average",
          unit: "g/10min",
          type: PropertyType.NUMBER,
          family: MaterialFamily.POLYMERS,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "2",
          propertyKey: "bet",
          description: "BET Surface Area",
          unit: "m²/g",
          type: PropertyType.NUMBER,
          family: MaterialFamily.FILLERS,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockPrismaService.propertyMetadata.findMany.mockResolvedValue(mockPropertyMetadata);

      const families = [MaterialFamily.POLYMERS, MaterialFamily.FILLERS];
      const result = await repository.findByFamilies(families);

      expect(mockPrismaService.propertyMetadata.findMany).toHaveBeenCalledWith({
        where: { family: { in: families } },
        orderBy: [{ family: "asc" }, { propertyKey: "asc" }],
      });
      expect(result).toEqual(mockPropertyMetadata);
      expect(result).toHaveLength(2);
    });

    it("should handle empty families array", async () => {
      mockPrismaService.propertyMetadata.findMany.mockResolvedValue([]);

      const result = await repository.findByFamilies([]);

      expect(mockPrismaService.propertyMetadata.findMany).toHaveBeenCalledWith({
        where: { family: { in: [] } },
        orderBy: [{ family: "asc" }, { propertyKey: "asc" }],
      });
      expect(result).toEqual([]);
    });
  });

  describe("error handling", () => {
    it("should handle database errors in findByFamily", async () => {
      const databaseError = new Error("Database connection failed");
      mockPrismaService.propertyMetadata.findMany.mockRejectedValue(databaseError);

      await expect(repository.findByFamily(MaterialFamily.POLYMERS)).rejects.toThrow("Database connection failed");
    });

    it("should handle database errors in findByFamilies", async () => {
      const databaseError = new Error("Database connection failed");
      mockPrismaService.propertyMetadata.findMany.mockRejectedValue(databaseError);

      await expect(repository.findByFamilies([MaterialFamily.POLYMERS])).rejects.toThrow("Database connection failed");
    });
  });
});
