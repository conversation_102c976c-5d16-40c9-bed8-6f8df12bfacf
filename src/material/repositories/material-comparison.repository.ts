import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma.service";
import { MaterialComparisonDto } from "../dto";
import { PropertyMetadataRepository } from "./property-metadata.repository";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

@Injectable()
export class MaterialComparisonRepository {
  constructor(
    private readonly prisma: PrismaService,
    private readonly propertyMetadataRepository: PropertyMetadataRepository,
  ) {}

  async getMaterialsForComparison(materialIds: string[], statusFilter?: MaterialStatus): Promise<MaterialComparisonDto[]> {
    const whereClause: any = {
      id: {
        in: materialIds,
      },
    };

    if (statusFilter) {
      whereClause.status = statusFilter;
    }

    const materials = await this.prisma.material.findMany({
      where: whereClause,
      include: {
        supplier: true,
        polymer: true,
        filler: true,
        elastomer: true,
        additive: true,
        recyclePolymer: true,
      },
    });

    return Promise.all(materials.map(material => this.mapMaterialToComparisonDto(material)));
  }

  async validateMaterialFamily(materialIds: string[]): Promise<MaterialFamily | null> {
    const materials = await this.prisma.material.findMany({
      where: {
        id: {
          in: materialIds,
        },
      },
      select: {
        family: true,
      },
    });

    if (materials.length === 0) {
      return null;
    }

    const firstFamily = materials[0].family;
    const allSameFamily = materials.every(material => material.family === firstFamily);

    return allSameFamily ? firstFamily : null;
  }

  private async mapMaterialToComparisonDto(material: any): Promise<MaterialComparisonDto> {
    const baseDto: MaterialComparisonDto = {
      id: material.id,
      reference: material.reference,
      type: material.type,
      family: material.family,
      status: material.status,
      supplierName: material.supplier.name,
      supplierId: material.supplier.id,
      supplierBatchNumber: material.supplierBatchNumber,
      origin: material.origin,
      properties: {},
    };

    switch (material.family) {
      case MaterialFamily.POLYMERS: {
        if (material.polymer) {
          baseDto.properties = await this.extractPropertiesFromObject(material.polymer, material.family);
        }
        break;
      }
      case MaterialFamily.FILLERS: {
        if (material.filler) {
          baseDto.properties = await this.extractPropertiesFromObject(material.filler, material.family);
        }
        break;
      }
      case MaterialFamily.ELASTOMERS: {
        if (material.elastomer) {
          baseDto.properties = await this.extractPropertiesFromObject(material.elastomer, material.family);
        }
        break;
      }
      case MaterialFamily.ADDITIVES: {
        if (material.additive) {
          baseDto.properties = await this.extractPropertiesFromObject(material.additive, material.family);
        }
        break;
      }
      case MaterialFamily.RECYCLE_POLYMERS: {
        if (material.recyclePolymer) {
          baseDto.properties = await this.extractPropertiesFromObject(material.recyclePolymer, material.family);
        }
        break;
      }
      default: {
        break;
      }
    }

    return baseDto;
  }

  private async extractPropertiesFromObject(
    sourceObject: any,
    family: MaterialFamily,
  ): Promise<Record<string, any>> {
    const result: Record<string, any> = {};
    const propertyMetadata = await this.propertyMetadataRepository.findByFamily(family);
    const propertiesToExtract = propertyMetadata.map(property => property.propertyKey);

    for (const property of propertiesToExtract) {
      if (sourceObject[property] !== undefined) {
        result[property] = sourceObject[property];
      }
    }

    return result;
  }
}
