import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma.service";
import { PropertyMetadataDto } from "../dto";
import { MaterialFamily, PropertyType } from "@/generated/prisma";

@Injectable()
export class PropertyMetadataRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findByFamily(family: MaterialFamily): Promise<PropertyMetadataDto[]> {
    return this.prisma.propertyMetadata.findMany({
      where: { family },
      orderBy: { propertyKey: "asc" },
    });
  }

  async findByFamilies(families: MaterialFamily[]): Promise<PropertyMetadataDto[]> {
    return this.prisma.propertyMetadata.findMany({
      where: { family: { in: families } },
      orderBy: [{ family: "asc" }, { propertyKey: "asc" }],
    });
  }

  async findAll(): Promise<PropertyMetadataDto[]> {
    return this.prisma.propertyMetadata.findMany({
      orderBy: [{ family: "asc" }, { propertyKey: "asc" }],
    });
  }

  async findByPropertyKey(propertyKey: string, family?: MaterialFamily): Promise<PropertyMetadataDto | null> {
    const where: any = { propertyKey };
    if (family) {
      where.family = family;
    }

    return this.prisma.propertyMetadata.findFirst({ where });
  }

  async createMany(properties: Omit<PropertyMetadataDto, "id">[]): Promise<number> {
    const result = await this.prisma.propertyMetadata.createMany({
      data: properties,
      skipDuplicates: true,
    });
    return result.count;
  }

  async upsert(propertyKey: string, family: MaterialFamily, data: Partial<PropertyMetadataDto>): Promise<PropertyMetadataDto> {
    return this.prisma.propertyMetadata.upsert({
      where: {
        propertyKey_family: {
          propertyKey,
          family,
        },
      },
      update: data,
      create: {
        propertyKey,
        family,
        description: data.description!,
        unit: data.unit,
        type: data.type || PropertyType.STRING,
      },
    });
  }

  async deleteAll(): Promise<number> {
    const result = await this.prisma.propertyMetadata.deleteMany();
    return result.count;
  }
}
