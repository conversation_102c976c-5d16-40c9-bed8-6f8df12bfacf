import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from "@nestjs/common";
import {
  ApiBearerAuth,
  ApiOAuth2,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { AuthGuard } from "../auth/auth.guard";
import { RoleGuard, Roles } from "../auth/role.guard";
import { AuthenticatedRequest } from "../common";
import { UserRole } from "../role/role.types";
import {
  MaterialComparisonRequestDto,
  MaterialComparisonResponseDto,
  MaterialSearchPageRequestDto,
  MaterialSearchRequestDto,
  MaterialSearchResponseDto,
  PaginatedMaterialPageResponseDto,
  PaginatedMaterialResponseDto,
  UpdateMaterialDto,
} from "./dto";
import { MaterialComparisonService } from "./material-comparison.service";
import { MaterialService } from "./material.service";

@ApiBearerAuth()
@ApiOAuth2([process.env.AZURE_API_SCOPE ?? ""])
@UseGuards(AuthGuard, RoleGuard)
@ApiTags("materials")
@Controller("materials")
export class MaterialController {
  public constructor(
    private readonly materialService: MaterialService,
    private readonly materialComparisonService: MaterialComparisonService,
  ) {}

  @Get("")
  @HttpCode(HttpStatus.OK)
  @Roles(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
    UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
    UserRole.MATERIAL_MANAGER,
  )
  @ApiOperation({
    description:
      "Search materials with multiple filter options including family, type, status, supplier, and more. Engineers can only see available materials.",
    summary: "Search materials with comprehensive filtering",
  })
  @ApiResponse({
    description: "Materials matching criteria retrieved successfully",
    status: HttpStatus.OK,
    type: PaginatedMaterialResponseDto,
  })
  public async getMaterials(
    @Query() searchRequest: MaterialSearchRequestDto,
    @Req() request?: AuthenticatedRequest,
  ): Promise<PaginatedMaterialResponseDto> {
    const userRole = request?.user?.role;
    return this.materialService.searchMaterials({ ...searchRequest, userRole });
  }

  @Get("origins")
  @HttpCode(HttpStatus.OK)
  @Roles(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
    UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
    UserRole.MATERIAL_MANAGER,
  )
  @ApiOperation({
    description: "Retrieve a list of all distinct material origins available in the system.",
    summary: "Get distinct material origins",
  })
  @ApiResponse({
    description: "Distinct material origins retrieved successfully",
    status: HttpStatus.OK,
    type: [String],
  })
  public async getMaterialOrigins(): Promise<string[]> {
    return this.materialService.getMaterialOrigins();
  }

  @Post("page")
  @HttpCode(HttpStatus.OK)
  @Roles(
    UserRole.ADMIN,
    UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
    UserRole.MATERIAL_MANAGER,
  )
  @ApiOperation({
    description:
      "Search materials with comprehensive filtering including family-specific criteria and include family-specific data (polymer, filler, elastomer, additive properties). Only materials from POLYMERS, FILLERS, ELASTOMERS, and ADDITIVES families are included, except FEEDSTOCK_RECYCLING_MEMBERS who can only access RECYCLE_POLYMERS. Supports advanced filtering with operators (>=, >, <=, <, =, between, contains) on family-specific properties.",
    summary: "Search materials with family relations and criteria",
  })
  @ApiResponse({
    description: "Materials with family relations retrieved successfully",
    status: HttpStatus.OK,
    type: PaginatedMaterialPageResponseDto,
  })
  public async getMaterialsPage(
    @Body() searchRequest: MaterialSearchPageRequestDto,
    @Req() request?: AuthenticatedRequest,
  ): Promise<PaginatedMaterialPageResponseDto> {
    const userRole = request?.user?.role;
    return this.materialService.searchMaterialsWithCriteria({ ...searchRequest, userRole });
  }

  @Post("compare")
  @HttpCode(HttpStatus.OK)
  @Roles(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
    UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
    UserRole.MATERIAL_MANAGER,
  )
  @ApiOperation({
    description:
      "Compare materials based on their properties. Materials can be from different families, and properties from all families will be included in the response. Returns dynamic properties based on material families in the comparison (Polymers, Fillers, Elastomers, Additives, Recycle Polymers). Access is role-based: Admin, Data Scientist, and Engineering Manager have full access. Engineers can only compare available materials.",
    summary: "Compare multiple materials",
  })
  @ApiResponse({
    description: "Material comparison completed successfully",
    status: HttpStatus.OK,
    type: MaterialComparisonResponseDto,
  })
  @ApiResponse({
    description:
      "Invalid request - invalid material count (must be 1-20 materials)",
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiResponse({
    description:
      "Business rule violation - Engineers can only compare available materials",
    status: HttpStatus.UNPROCESSABLE_ENTITY,
  })
  @ApiResponse({
    description: "One or more materials not found",
    status: HttpStatus.NOT_FOUND,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: HttpStatus.UNAUTHORIZED,
  })
  @ApiResponse({
    description: "Forbidden - Insufficient permissions for your role",
    status: HttpStatus.FORBIDDEN,
  })
  public async compareMaterials(
    @Body() request: MaterialComparisonRequestDto,
    @Req() requestObject?: AuthenticatedRequest,
  ): Promise<MaterialComparisonResponseDto> {
    const userRole = requestObject?.user?.role;
    return this.materialComparisonService.compareMaterials(request, userRole);
  }

  @Patch(":id")
  @HttpCode(HttpStatus.OK)
  @Roles(
    UserRole.ADMIN,
    UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
    UserRole.MATERIAL_MANAGER,
  )
  @ApiOperation({
    description: "Update material properties. Only admins and material managers can update materials.",
    summary: "Update material by ID",
  })
  @ApiParam({
    description: "Material ID",
    example: "material-uuid",
    name: "id",
  })
  @ApiResponse({
    description: "Material updated successfully",
    status: HttpStatus.OK,
    type: MaterialSearchResponseDto,
  })
  @ApiResponse({
    description: "Material not found",
    status: HttpStatus.NOT_FOUND,
  })
  @ApiResponse({
    description: "Invalid input data",
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: HttpStatus.UNAUTHORIZED,
  })
  @ApiResponse({
    description: "Forbidden - Insufficient permissions for your role",
    status: HttpStatus.FORBIDDEN,
  })
  public async updateMaterial(
    @Param("id") id: string,
    @Body() updateMaterialDto: UpdateMaterialDto,
    @Req() request?: AuthenticatedRequest,
  ): Promise<MaterialSearchResponseDto> {
    const userRole = request?.user?.role;
    return this.materialService.updateMaterial(id, updateMaterialDto, userRole);
  }
}
