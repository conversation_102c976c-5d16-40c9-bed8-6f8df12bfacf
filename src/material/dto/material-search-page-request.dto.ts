import { ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsEnum, IsNumber, IsOptional, IsString, Max, Min, ValidateNested } from "class-validator";
import { FamilyCriteriaDto } from "./family-criteria.dto";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

export class MaterialSearchPageRequestDto {
  @ApiPropertyOptional({
    description: "Search across all material fields (type, reference, family, supplier name, origin, supplier batch number)",
    example: "polymer",
  })
  @IsOptional()
  @IsString()
  public search?: string;

  @ApiPropertyOptional({
    description: "Filter materials by origin (e.g., Europe, Asia, North America). Can be a single string or array of strings",
    example: "Europe",
    oneOf: [
      { type: "string" },
      { items: { type: "string" }, type: "array" },
    ],
  })
  @IsOptional()
  @IsString({ each: true })
  public origin?: string | string[];

  @ApiPropertyOptional({
    description: "Filter materials by family",
    enum: MaterialFamily,
    example: MaterialFamily.POLYMERS,
  })
  @IsOptional()
  @IsEnum(MaterialFamily)
  public family?: MaterialFamily;

  @ApiPropertyOptional({
    description: "Filter materials by status. Can be a single status or array of statuses",
    example: MaterialStatus.AVAILABLE,
    oneOf: [
      { enum: Object.values(MaterialStatus) },
      { items: { enum: Object.values(MaterialStatus) }, type: "array" },
    ],
  })
  @IsOptional()
  @IsEnum(MaterialStatus, { each: true })
  public status?: MaterialStatus | MaterialStatus[];

  @ApiPropertyOptional({
    description: "Filter materials by supplier name",
  })
  @IsOptional()
  @IsString()
  public supplier?: string;

  @ApiPropertyOptional({
    description: "Filter materials by supplier batch number",
  })
  @IsOptional()
  @IsString()
  public supplierBatchNumber?: string;

  @ApiPropertyOptional({
    description: "Filter materials by reference",
  })
  @IsOptional()
  @IsString()
  public reference?: string;

  @ApiPropertyOptional({
    description: "Page number for pagination",
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  public page?: number;

  @ApiPropertyOptional({
    description: "Number of items per page",
    example: 10,
    maximum: 100,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  public limit?: number;

  @ApiPropertyOptional({
    description: "Array of criteria for filtering family-specific properties",
    example: [
      {
        operator: ">=",
        propertyName: "mfiAv",
        value: 10,
      },
      {
        maxValue: 1.2,
        minValue: 0.9,
        operator: "between",
        propertyName: "densityAv",
      },
    ],
    type: [FamilyCriteriaDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FamilyCriteriaDto)
  public familyCriteria?: FamilyCriteriaDto[];
}
