import { ApiProperty } from "@nestjs/swagger";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

export class MaterialComparisonDto {
  @ApiProperty({
    description: "Material ID",
    example: "uuid1",
  })
  id: string;

  @ApiProperty({
    description: "Material reference/name",
    example: "rPP1",
  })
  reference: string;

  @ApiProperty({
    description: "Material type",
    example: "PP grade 2",
  })
  type: string;

  @ApiProperty({
    description: "Material family",
    enum: MaterialFamily,
    example: MaterialFamily.POLYMERS,
  })
  family: MaterialFamily;

  @ApiProperty({
    description: "Material status",
    enum: MaterialStatus,
    example: MaterialStatus.AVAILABLE,
  })
  status: MaterialStatus;

  @ApiProperty({
    description: "Supplier name",
    example: "Anonymous",
  })
  supplierName: string;

  @ApiProperty({
    description: "Supplier ID",
    example: "supplier-uuid",
  })
  supplierId: string;

  @ApiProperty({
    description: "Batch number",
    example: "BCF 3366",
  })
  supplierBatchNumber: string;

  @ApiProperty({
    description: "Material origin/region",
    example: "Europe",
  })
  origin: string;

  @ApiProperty({
    description: "Dynamic properties based on material family",
    example: {
      mfiAv: 25,
      densityAv: 1.2,
      stressBreakAv: 30,
    },
  })
  properties: Record<string, any>;
}

export class MaterialComparisonResponseDto {
  @ApiProperty({
    description: "Array of compared materials with their properties",
    type: [MaterialComparisonDto],
  })
  materials: MaterialComparisonDto[];

  @ApiProperty({
    description: "Available properties for this material family",
    type: [String],
    example: ["mfiAv", "densityAv", "stressBreakAv"],
  })
  availableProperties: string[];

  @ApiProperty({
    description: "Property metadata (units, descriptions, etc.)",
    example: {
      mfiAv: { unit: "g/10min", description: "Melt Flow Index Average" },
      densityAv: { unit: "g/cm³", description: "Density Average" },
    },
  })
  propertyMetadata: Record<string, any>;
}
