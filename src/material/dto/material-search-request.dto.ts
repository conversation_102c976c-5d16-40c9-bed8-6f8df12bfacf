import { ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsOptional, IsString, IsEnum, IsNumber, Min, Max } from "class-validator";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

export class MaterialSearchRequestDto {
  @ApiPropertyOptional({
    description: "Search across all material fields (type, reference, family, supplier name, origin, supplier batch number)",
    example: "polymer",
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: "Filter materials by origin (e.g., Europe, Asia, North America)",
  })
  @IsOptional()
  @IsString()
  origin?: string;

  @ApiPropertyOptional({
    description: "Filter materials by family",
    enum: MaterialFamily,
    example: MaterialFamily.POLYMERS,
  })
  @IsOptional()
  @IsEnum(MaterialFamily)
  family?: MaterialFamily;

  @ApiPropertyOptional({
    description: "Filter materials by status",
    enum: MaterialStatus,
    example: MaterialStatus.AVAILABLE,
  })
  @IsOptional()
  @IsEnum(MaterialStatus)
  status?: MaterialStatus;

  @ApiPropertyOptional({
    description: "Filter materials by supplier name (partial match)",
  })
  @IsOptional()
  @IsString()
  supplier?: string;

  @ApiPropertyOptional({
    description: "Filter materials by supplier batch number",
  })
  @IsOptional()
  @IsString()
  supplierBatchNumber?: string;

  @ApiPropertyOptional({
    description: "Filter materials by reference",
  })
  @IsOptional()
  @IsString()
  reference?: string;

  @ApiPropertyOptional({
    description: "Page number for pagination",
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: "Number of items per page",
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;
}
