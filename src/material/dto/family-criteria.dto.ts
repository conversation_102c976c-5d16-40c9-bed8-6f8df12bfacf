import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsN<PERSON>ber, IsOptional, IsString } from "class-validator";
import { CriteriaOperator } from "@/common/dto/criteria-operator.dto";

export class FamilyCriteriaDto {
  @ApiProperty({
    description: "Property name to filter by",
    example: "mfiAv",
  })
  @IsString()
  public propertyName: string;

  @ApiProperty({
    description: "Comparison operator",
    enum: CriteriaOperator,
    example: CriteriaOperator.GREATER_THAN_OR_EQUAL,
  })
  @IsEnum(CriteriaOperator)
  public operator: CriteriaOperator;

  @ApiPropertyOptional({
    description: "Value for comparison (used with single value operators)",
    example: 10,
    oneOf: [
      { type: "number" },
      { type: "string" },
    ],
  })
  @IsOptional()
  public value?: number | string;

  @ApiPropertyOptional({
    description: "Minimum value for range comparison",
    example: 5,
  })
  @IsOptional()
  @IsNumber()
  public minValue?: number;

  @ApiPropertyOptional({
    description: "Maximum value for range comparison",
    example: 15,
  })
  @IsOptional()
  @IsNumber()
  public maxValue?: number;
}
