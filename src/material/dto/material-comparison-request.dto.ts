import { ApiProperty } from "@nestjs/swagger";
import { IsArray, IsOptional, IsString, IsUUID } from "class-validator";

export class MaterialComparisonRequestDto {
  @ApiProperty({
    description: "Array of material IDs to compare",
    type: [String],
    example: ["uuid1", "uuid2", "uuid3"],
    minItems: 2,
    maxItems: 10,
  })
  @IsArray()
  @IsUUID(4, { each: true })
  materialIds: string[];

  @ApiProperty({
    description: "Include only specific properties in comparison (optional - returns all if not specified)",
    type: [String],
    example: ["mfiAv", "densityAv", "stressBreakAv"],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  includeProperties?: string[];
}
