import { ApiProperty } from "@nestjs/swagger";
import { AdditiveFamilyDataDto } from "./family-data/additive.dto";
import { ElastomerFamilyDataDto } from "./family-data/elastomer.dto";
import { FillerFamilyDataDto } from "./family-data/filler.dto";
import { PolymerFamilyDataDto } from "./family-data/polymer.dto";
import { RecyclePolymerFamilyDataDto } from "./family-data/recycle-polymer.dto";
import { MaterialSearchResponseDto } from "./material-search-response.dto";

export class MaterialPageResponseDto extends MaterialSearchResponseDto {
  @ApiProperty({
    description: "Family-specific data (polymer, filler, elastomer, additive)",
    example: { densityAv: 0.95, mfiAv: 12.5 },
    required: false,
  })
  public familyData?: PolymerFamilyDataDto | FillerFamilyDataDto | ElastomerFamilyDataDto | AdditiveFamilyDataDto | RecyclePolymerFamilyDataDto;
}
