import { ApiProperty } from "@nestjs/swagger";
import { MaterialStatus } from "@/generated/prisma";

export class MaterialSearchResponseDto {
  @ApiProperty({
    description: "Material ID",
    example: "f4498fc2-0acd-4a12-b483-055962762479",
  })
  id: string;

  @ApiProperty({
    description: "Material type",
    example: "Virgin PP Homopolymer",
  })
  type: string;

  @ApiProperty({
    description: "Material reference",
    example: "A100-80",
  })
  reference: string;

  @ApiProperty({
    description: "Material family",
    example: "POLYMERS",
  })
  family: string;

  @ApiProperty({
    description: "Material status",
    enum: MaterialStatus,
    example: MaterialStatus.AVAILABLE,
  })
  status: MaterialStatus;

  @ApiProperty({
    description: "Supplier name",
    required: false,
    nullable: true,
    example: "Cibas",
  })
  supplier: string | null;

  @ApiProperty({
    description: "Material origin",
    required: false,
    nullable: true,
    example: "Asia",
  })
  origin: string | null;

  @ApiProperty({
    description: "Supplier batch number",
    required: false,
    nullable: true,
    example: "BATCH-12345",
  })
  supplierBatchNumber: string | null;
}
