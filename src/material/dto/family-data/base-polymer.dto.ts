import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsDateString, IsNumber, IsOptional, IsString } from "class-validator";

export class BasePolymerFamilyDataDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  public resultUpdateDate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public validationEngineering?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public tds?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public priceExw?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public priceExwDate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public comment?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public mfiNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public mfiTestConditions?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public mfiAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public mfiStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public densityNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public densityAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public densityStdDv?: number;
}
