import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsNumber, IsOptional, IsString } from "class-validator";
import { BasePolymerFamilyDataDto } from "./base-polymer.dto";

export class RecyclePolymerFamilyDataDto extends BasePolymerFamilyDataDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public technicalProfileAvgValue?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public feedstockOrUseInCompounds?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public color?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public pirPcrElv?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public wasteDetails?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public materialForm?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public productVolumesKtY?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public volumesAvailableForMactKtY?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public certificatesReachRohs?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public endOfWasteStatus?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public filtrationLocation?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public systemFiltration?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public filtrationSize?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public quantityFilteredRemaining?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public d22NbFiltersUsed?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public d22NbFilterPerKgFeedstock?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public d32QuantityScrap?: number;
}
