import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsNumber, IsOptional, IsString } from "class-validator";
import { BasePolymerFamilyDataDto } from "./base-polymer.dto";

export class PolymerFamilyDataDto extends BasePolymerFamilyDataDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public tensileModulusNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public tensileModulusCond?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public tensileModulusAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public tensileModulusStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public flexuralModulusNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public flexuralModulusAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public flexuralModulusStdDev?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public flexuralStressFcAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public flexuralStressFcStdDev?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public stressBreakNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public stressBreakAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public stressBreakStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public stressYieldAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public stressYieldStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public yieldStrainAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public yieldStrainStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public strainBreakNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public strainBreakAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public strainBreakStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public nominalStrainBreakAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public nominalStrainBreakStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public notchedIzodNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public notchedIzodAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public notchedIzodStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public notchedIzodFailureType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public unnotchedIzodNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public unnotchedIzodAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public unnotchedIzodStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public unnotchedIzodFailureType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public hdtBNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public hdtBAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public hdtBStdDv?: number;
}
