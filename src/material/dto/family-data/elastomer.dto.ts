import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsNumber, IsOptional, IsString } from "class-validator";

export class ElastomerFamilyDataDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public density?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public mfi190216?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public codeanonymElasto?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public nIzod23?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public flexModulus?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public tracModulus100?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public elongAtBreak?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public mfi230216?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public meltingPoint?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public hdtB?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public hdtA?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public shoreA?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public shoreD?: number;
}
