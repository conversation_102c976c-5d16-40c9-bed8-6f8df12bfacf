import { Injectable, NotFoundException } from "@nestjs/common";
import { RoleResponseDto } from "./dto";
import { RoleRepository } from "./repositories";

@Injectable()
export class RoleService {
  constructor(private readonly roleRepository: RoleRepository) {}

  async findOne(id: string): Promise<RoleResponseDto> {
    const role = await this.roleRepository.findById(id);

    if (!role) {
      throw new NotFoundException("Role not found");
    }

    return role;
  }

  async findOneOrThrow(id: string): Promise<RoleResponseDto> {
    const role = await this.roleRepository.findById(id);

    if (!role) {
      throw new NotFoundException(`Role with ID '${id}' not found`);
    }

    return role;
  }

  async getRoleNames(): Promise<string[]> {
    return this.roleRepository.getRoleNames();
  }

  async findAll(): Promise<RoleResponseDto[]> {
    return this.roleRepository.findAll();
  }

  async findByCode(code: string): Promise<RoleResponseDto> {
    const role = await this.roleRepository.findByCode(code);

    if (!role) {
      throw new NotFoundException(`Role with code '${code}' not found`);
    }

    return role;
  }
}
