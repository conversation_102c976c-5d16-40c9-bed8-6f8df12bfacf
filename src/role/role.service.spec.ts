import { NotFoundException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { RoleRepository } from "./repositories";
import { RoleService } from "./role.service";

describe("RoleService", () => {
  let service: RoleService;
  let repository: RoleRepository;

  const mockRole = { id: "1", name: "Test Role", code: "TEST_ROLE" };
  const mockRoles = [mockRole];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoleService,
        {
          provide: RoleRepository,
          useValue: {
            findById: jest.fn().mockImplementation((id: string) => {
              if (id === "1") return Promise.resolve(mockRole);
              return Promise.resolve(null);
            }),
            getRoleNames: jest.fn().mockResolvedValue(["Test Role"]),
            findAll: jest.fn().mockResolvedValue(mockRoles),
            findByCode: jest.fn().mockImplementation((code: string) => {
              if (code === "TEST_ROLE") return Promise.resolve(mockRole);
              return Promise.resolve(null);
            }),
          },
        },
      ],
    }).compile();

    service = module.get<RoleService>(RoleService);
    repository = module.get<RoleRepository>(RoleRepository);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("findOne", () => {
    it("should return a single role if found", async () => {
      const role = await service.findOne("1");
      expect(role).toEqual(mockRole);
      expect(repository.findById).toHaveBeenCalledWith("1");
    });

    it("should throw a NotFoundException if the role is not found", async () => {
      await expect(service.findOne("2")).rejects.toThrow(new NotFoundException("Role not found"));
    });
  });

  describe("findOneOrThrow", () => {
    it("should return a single role if found", async () => {
      const role = await service.findOneOrThrow("1");
      expect(role).toEqual(mockRole);
      expect(repository.findById).toHaveBeenCalledWith("1");
    });

    it("should throw a NotFoundException if the role is not found", async () => {
      await expect(service.findOneOrThrow("2")).rejects.toThrow(new NotFoundException("Role with ID '2' not found"));
    });
  });

  describe("getRoleNames", () => {
    it("should return an array of role names", async () => {
      const roleNames = await service.getRoleNames();
      expect(roleNames).toEqual(["Test Role"]);
      expect(repository.getRoleNames).toHaveBeenCalled();
    });
  });

  describe("findAll", () => {
    it("should return an array of roles", async () => {
      const roles = await service.findAll();
      expect(roles).toEqual(mockRoles);
      expect(repository.findAll).toHaveBeenCalled();
    });
  });

  describe("findByCode", () => {
    it("should return a single role if found", async () => {
      const role = await service.findByCode("TEST_ROLE");
      expect(role).toEqual(mockRole);
      expect(repository.findByCode).toHaveBeenCalledWith("TEST_ROLE");
    });

    it("should throw a NotFoundException if the role is not found", async () => {
      await expect(service.findByCode("INVALID_CODE")).rejects.toThrow(new NotFoundException("Role with code 'INVALID_CODE' not found"));
    });
  });
});
