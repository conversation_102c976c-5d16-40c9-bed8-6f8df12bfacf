import { Test, TestingModule } from "@nestjs/testing";
import { RoleRepository } from "./role.repository";
import { PrismaService } from "@/prisma.service";

describe("RoleRepository", () => {
  let repo: RoleRepository;
  interface RoleMock {
    findMany: jest.Mock
    findUnique: jest.Mock
  }
  interface PrismaMock {
    role: RoleMock
  }
  let prisma: PrismaMock;

  beforeEach(async () => {
    prisma = {
      role: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoleRepository,
        { provide: PrismaService, useValue: prisma },
      ],
    }).compile();

    repo = module.get(RoleRepository);
  });

  it("findById should query by id", async () => {
    prisma.role.findUnique.mockResolvedValue({ id: "r1", name: "<PERSON><PERSON>" });
    const result = await repo.findById("r1");
    expect(prisma.role.findUnique).toHaveBeenCalledWith({ where: { id: "r1" } });
    expect(result).toEqual({ id: "r1", name: "Admin" });
  });

  it("getRoleNames should return names sorted", async () => {
    prisma.role.findMany.mockResolvedValue([{ name: "Admin" }, { name: "User" }]);
    const result = await repo.getRoleNames();
    expect(prisma.role.findMany).toHaveBeenCalledWith({
      orderBy: { name: "asc" },
      select: { name: true },
    });
    expect(result).toEqual(["Admin", "User"]);
  });

  it("findAll should return all roles sorted", async () => {
    prisma.role.findMany.mockResolvedValue([{ id: "r1", name: "Admin" }]);
    const result = await repo.findAll();
    expect(prisma.role.findMany).toHaveBeenCalledWith({
      orderBy: { name: "asc" },
    });
    expect(result).toEqual([{ id: "r1", name: "Admin" }]);
  });

  it("findByCode should query by code", async () => {
    prisma.role.findUnique.mockResolvedValue({ id: "r2", name: "Manager" });
    const result = await repo.findByCode("MANAGER");
    expect(prisma.role.findUnique).toHaveBeenCalledWith({ where: { code: "MANAGER" } });
    expect(result).toEqual({ id: "r2", name: "Manager" });
  });
});
