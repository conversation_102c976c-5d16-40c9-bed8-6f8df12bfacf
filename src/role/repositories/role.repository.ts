import { Injectable } from "@nestjs/common";
import { PrismaService } from "@/prisma.service";

export interface RoleData {
  id: string
  name: string
}

@Injectable()
export class RoleRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findById(id: string): Promise<RoleData | null> {
    return this.prisma.role.findUnique({
      where: { id },
    });
  }

  async getRoleNames(): Promise<string[]> {
    const roles = await this.prisma.role.findMany({
      select: { name: true },
      orderBy: { name: "asc" },
    });
    return roles.map(role => role.name);
  }

  async findAll(): Promise<RoleData[]> {
    return this.prisma.role.findMany({
      orderBy: { name: "asc" },
    });
  }

  async findByCode(code: string): Promise<RoleData | null> {
    return this.prisma.role.findUnique({
      where: { code },
    });
  }
}
