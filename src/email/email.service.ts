import { EmailClient, EmailMessage } from "@azure/communication-email";
import { Injectable, Inject, Logger } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { NewFormulationRequestEvent } from "../request/events/new-formulation-request.event";
import { EMAIL_ENABLED_TOKEN } from "./email.constants";

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly emailClient: EmailClient;
  private readonly senderAddress: string;
  private readonly isEmailEnabled: boolean;

  constructor(
    @Inject(EMAIL_ENABLED_TOKEN) isEmailEnabled: boolean
  ) {
    const connectionString = process.env.AZURE_COMMUNICATION_SERVICE_CONNECTION_STRING || "<your-acs-connection-string>";
    this.senderAddress = process.env.ACS_SENDER_EMAIL || "<your-sender-email>";
    this.emailClient = new EmailClient(connectionString);
    this.isEmailEnabled = isEmailEnabled;
  }

  /**
   * Send an email to a list of users, similar to createNotification.
   * @param params - { emails: string[], title: string, message: string, html?: string }
   * @param params.emails - List of email addresses to send the notification to.
   * @param params.title - The title of the email.
   * @param params.message - The message content of the email.
   * @param params.html - Optional HTML content for the email.
   * @param parameters
   * @param parameters.emails
   * @param parameters.title
   * @param parameters.message
   * @param parameters.html
   */
  async send(parameters: { emails: string[], title: string, message: string, html?: string }): Promise<void> {
    const { emails, title, message, html } = parameters;

    if (emails.length === 0) return;

    if (!this.isEmailEnabled) {
      return;
    }
    const htmlContent = html || `<html><body><h2>${title}</h2><p>${message}</p></body></html>`;
    const emailMessage: EmailMessage = {
      senderAddress: this.senderAddress,
      content: {
        subject: title,
        html: htmlContent,
      },
      recipients: {
        to: emails.map(email => ({ address: email })),
      },
    };
    await this.emailClient.beginSend(emailMessage);
  }

  @OnEvent("request.new-formulation")
  async handleNewFormulationRequest(event: NewFormulationRequestEvent) {
    if (!this.isEmailEnabled) return;
    if (!event.managerEmails || event.managerEmails.length === 0) return;

    await this.send({
      emails: event.managerEmails,
      title: "New Formulation Request Submitted",
      message: event.message,
      html: event.html,
    });
  }
}
