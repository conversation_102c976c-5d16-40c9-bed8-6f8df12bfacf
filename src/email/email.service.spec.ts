import { EmailClient } from "@azure/communication-email";
import { Test, TestingModule } from "@nestjs/testing";
import { EMAIL_ENABLED_TOKEN } from "./email.constants";
import { EmailService } from "./email.service";

// Mock the EmailClient
jest.mock("@azure/communication-email", () => ({
  EmailClient: jest.fn().mockImplementation(() => ({
    beginSend: jest.fn(),
  })),
}));

describe("EmailService", () => {
  let service: EmailService;
  let mockEmailClient: jest.Mocked<EmailClient>;

  const createTestingModule = async (isEmailEnabled = true) => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: EMAIL_ENABLED_TOKEN,
          useValue: isEmailEnabled,
        },
      ],
    }).compile();

    return module;
  };

  beforeEach(async () => {
    process.env.ACS_SENDER_EMAIL = "<your-sender-email>";
    const module = await createTestingModule(true);
    service = module.get<EmailService>(EmailService);
    mockEmailClient = (service as any).emailClient;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  it("should send email with provided parameters", async () => {
    const parameters = {
      emails: ["<EMAIL>", "<EMAIL>"],
      title: "Test Subject",
      message: "Test message content",
      html: "<html><body><h1>Custom HTML</h1></body></html>",
    };

    mockEmailClient.beginSend = jest.fn().mockResolvedValue({ messageId: "test-id" });

    await service.send(parameters);

    expect(mockEmailClient.beginSend).toHaveBeenCalledWith({
      senderAddress: "<your-sender-email>",
      content: {
        subject: "Test Subject",
        html: "<html><body><h1>Custom HTML</h1></body></html>",
      },
      recipients: {
        to: [
          { address: "<EMAIL>" },
          { address: "<EMAIL>" },
        ],
      },
    });
  });

  it("should use default HTML template when html is not provided", async () => {
    const parameters = {
      emails: ["<EMAIL>"],
      title: "Test Subject",
      message: "Test message",
    };

    mockEmailClient.beginSend = jest.fn().mockResolvedValue({ messageId: "test-id" });

    await service.send(parameters);

    expect(mockEmailClient.beginSend).toHaveBeenCalledWith({
      senderAddress: "<your-sender-email>",
      content: {
        subject: "Test Subject",
        html: "<html><body><h2>Test Subject</h2><p>Test message</p></body></html>",
      },
      recipients: {
        to: [{ address: "<EMAIL>" }],
      },
    });
  });

  it("should not send email when emails array is empty", async () => {
    const parameters = {
      emails: [],
      title: "Test Subject",
      message: "Test message",
    };

    await service.send(parameters);

    expect(mockEmailClient.beginSend).not.toHaveBeenCalled();
  });

  it("should handle single email address", async () => {
    const parameters = {
      emails: ["<EMAIL>"],
      title: "Single Email Test",
      message: "Single email message",
    };

    mockEmailClient.beginSend = jest.fn().mockResolvedValue({ messageId: "test-id" });

    await service.send(parameters);

    expect(mockEmailClient.beginSend).toHaveBeenCalledWith({
      senderAddress: "<your-sender-email>",
      content: {
        subject: "Single Email Test",
        html: "<html><body><h2>Single Email Test</h2><p>Single email message</p></body></html>",
      },
      recipients: {
        to: [{ address: "<EMAIL>" }],
      },
    });
  });

  it("should handle emails with special characters", async () => {
    const parameters = {
      emails: ["<EMAIL>"],
      title: "Subject with émojis 🎉",
      message: "Message with special chars: & < > \"",
    };

    mockEmailClient.beginSend = jest.fn().mockResolvedValue({ messageId: "test-id" });

    await service.send(parameters);

    expect(mockEmailClient.beginSend).toHaveBeenCalledWith(
      expect.objectContaining({
        content: {
          subject: "Subject with émojis 🎉",
          html: "<html><body><h2>Subject with émojis 🎉</h2><p>Message with special chars: & < > \"</p></body></html>",
        },
        recipients: {
          to: [{ address: "<EMAIL>" }],
        },
      })
    );
  });

  it("should propagate errors from EmailClient", async () => {
    const parameters = {
      emails: ["<EMAIL>"],
      title: "Test Subject",
      message: "Test message",
    };

    const error = new Error("EmailClient error");
    mockEmailClient.beginSend = jest.fn().mockRejectedValue(error);

    await expect(service.send(parameters)).rejects.toThrow("EmailClient error");
  });

  describe("Feature Flag Tests", () => {
    it("should handle NewFormulationRequestEvent and call send", async () => {
      const spy = jest.spyOn(service, "send").mockResolvedValue();
      const event = {
        managerEmails: ["<EMAIL>"],
        managerIds: ["manager1"],
        requesterId: "user1",
        formulationName: "FormA",
        message: "Test message",
        html: "<html><body>Test</body></html>",
      };

      await service.handleNewFormulationRequest(event);
      expect(spy).toHaveBeenCalledWith({
        emails: ["<EMAIL>"],
        title: "New Formulation Request Submitted",
        message: "Test message",
        html: "<html><body>Test</body></html>",
      });
    });

    it("should not send email when feature flag is disabled", async () => {
      const module = await createTestingModule(false);
      const disabledService = module.get<EmailService>(EmailService);
      const disabledMockClient = (disabledService as any).emailClient;

      const parameters = {
        emails: ["<EMAIL>"],
        title: "Test Subject",
        message: "Test message",
      };

      await disabledService.send(parameters);

      expect(disabledMockClient.beginSend).not.toHaveBeenCalled();
    });

    it("should send email when feature flag is enabled", async () => {
      const parameters = {
        emails: ["<EMAIL>"],
        title: "Test Subject",
        message: "Test message",
      };

      mockEmailClient.beginSend = jest.fn().mockResolvedValue({ messageId: "test-id" });

      await service.send(parameters);

      expect(mockEmailClient.beginSend).toHaveBeenCalled();
    });
  });
});
