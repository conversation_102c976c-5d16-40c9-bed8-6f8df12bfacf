import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";
import { AuthService } from "./auth.service";
import { AuthenticatedRequest } from "@/common";
import { UserRole } from "@/role/role.types";

@Injectable()
export class AuthGuard implements CanActivate {
  public constructor(
    private readonly authService: AuthService,
  ) {}

  public async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();

    if (process.env.NODE_ENV === "development" && process.env.BYPASS_AUTH === "true") {
      request.user = {
        oid: "test-oid",
        role: UserRole.UNKNOWN,
      };
      return true;
    }

    const token = request.headers["authorization"]?.split(" ")[1];
    if (!token) {
      return false;
    }

    try {
      const user = await this.authService.validateToken(token);
      request.user = user as { oid: string, role: UserRole };
      return true;
    }
    catch {
      return false;
    }
  }
}
