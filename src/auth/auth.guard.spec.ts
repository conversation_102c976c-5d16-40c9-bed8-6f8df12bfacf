import { ExecutionContext } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { AuthGuard } from "./auth.guard";
import { AuthService } from "./auth.service";
import { UserRole } from "@/role/role.types";

const mockExecutionContext = (headers: any) => {
  const request = {
    headers,
    user: undefined,
  };
  return {
    switchToHttp: () => ({
      getRequest: () => request,
    }),
  } as ExecutionContext;
};

describe("AuthGuard", () => {
  let guard: AuthGuard;
  let authService: AuthService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthGuard,
        {
          provide: AuthService,
          useValue: {
            validateToken: jest.fn(),
          },
        },
      ],
    }).compile();

    guard = module.get<AuthGuard>(AuthGuard);
    authService = module.get<AuthService>(AuthService);
  });

  it("should be defined", () => {
    expect(guard).toBeDefined();
  });

  it("should return true if BYPASS_AUTH is true in development", async () => {
    process.env.NODE_ENV = "development";
    process.env.BYPASS_AUTH = "true";
    const context = mockExecutionContext({});
    const canActivate = await guard.canActivate(context);
    expect(canActivate).toBe(true);
    expect((context.switchToHttp().getRequest()).user).toEqual({
      oid: "test-oid",
      role: UserRole.UNKNOWN,
    });
    process.env.NODE_ENV = "test";
    process.env.BYPASS_AUTH = "false";
  });

  it("should return false if no token is provided", async () => {
    const context = mockExecutionContext({ authorization: "" });
    const canActivate = await guard.canActivate(context);
    expect(canActivate).toBe(false);
  });

  it("should return false on token validation error", async () => {
    (authService.validateToken as jest.Mock).mockRejectedValue(new Error("token validation failed"));
    const context = mockExecutionContext({ authorization: "Bearer invalid-token" });
    const canActivate = await guard.canActivate(context);
    expect(canActivate).toBe(false);
  });

  it("should return true if token is valid", async () => {
    const user = { oid: "test-oid", role: UserRole.ADMIN };
    (authService.validateToken as jest.Mock).mockResolvedValue(user);
    const context = mockExecutionContext({ authorization: "Bearer valid-token" });
    const canActivate = await guard.canActivate(context);
    expect(canActivate).toBe(true);
    expect((context.switchToHttp().getRequest()).user).toEqual(user);
  });
});
