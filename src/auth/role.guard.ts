import { Injectable, CanActivate, ExecutionContext, SetMetadata, UnauthorizedException } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { AuthService } from "./auth.service";

export const Roles = (...roles: string[]) => SetMetadata("roles", roles);

@Injectable()
export class RoleGuard implements CanActivate {
  constructor(
    private readonly authService: AuthService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredRoles = this.reflector.get<string[]>("roles", context.getHandler());

    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const token = request.headers.authorization?.split(" ")[1];

    if (!token) {
      throw new UnauthorizedException("No token provided");
    }

    try {
      const payload = await this.authService.validateToken(token);

      if (typeof payload === "object" && "oid" in payload) {
        const userId = payload.oid as string;
        const userRole = payload.role as string;
        if (!userId || !userRole) {
          return false;
        }

        return requiredRoles.includes(String(payload.role).toUpperCase());
      }

      return false;
    }
    catch {
      throw new UnauthorizedException("Invalid token");
    }
  }
}
