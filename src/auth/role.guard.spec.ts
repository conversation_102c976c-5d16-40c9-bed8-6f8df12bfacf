import { ExecutionContext, UnauthorizedException } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Test, TestingModule } from "@nestjs/testing";
import { AuthService } from "./auth.service";
import { RoleGuard, Roles } from "./role.guard";

const mockExecutionContext = (headers: unknown, handler: unknown) => {
  const request = {
    headers,
    user: undefined,
  };
  return {
    getHandler: () => handler,
    switchToHttp: () => ({
      getRequest: () => request,
    }),
  } as unknown as ExecutionContext;
};

describe("RoleGuard", () => {
  let guard: RoleGuard;
  let authService: AuthService;
  let reflector: Reflector;

  const handler = jest.fn();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoleGuard,
        {
          provide: AuthService,
          useValue: {
            validateToken: jest.fn(),
          },
        },
        Reflector,
      ],
    }).compile();

    guard = module.get<RoleGuard>(RoleGuard);
    authService = module.get<AuthService>(AuthService);
    reflector = module.get<Reflector>(Reflector);
  });

  it("should be defined", () => {
    expect(guard).toBeDefined();
  });

  it("should return true if no roles are required", async () => {
    jest.spyOn(reflector, "get").mockReturnValue(false);
    const context = mockExecutionContext({}, handler);
    expect(await guard.canActivate(context)).toBe(true);
  });

  it("should throw UnauthorizedException if no token is provided", async () => {
    Roles("some-role")(handler);
    jest.spyOn(reflector, "get").mockReturnValue(["some-role"]);
    const context = mockExecutionContext({ authorization: "" }, handler);
    await expect(guard.canActivate(context)).rejects.toThrow(new UnauthorizedException("No token provided"));
  });

  it("should return true if user has the required role", async () => {
    Roles("TEST-ROLE")(handler);
    jest.spyOn(reflector, "get").mockReturnValue(["TEST-ROLE"]);
    (authService.validateToken as jest.Mock).mockResolvedValue({ oid: "test-oid", role: "TEST-ROLE" });
    const context = mockExecutionContext({ authorization: "Bearer valid-token" }, handler);
    const canActivate = await guard.canActivate(context);
    expect(canActivate).toBe(true);
  });

  it("should return false if user does not have the required role", async () => {
    Roles("ADMIN-ROLE")(handler);
    jest.spyOn(reflector, "get").mockReturnValue(["ADMIN-ROLE"]);
    (authService.validateToken as jest.Mock).mockResolvedValue({ oid: "test-oid", role: "TEST-ROLE" });
    const context = mockExecutionContext({ authorization: "Bearer valid-token" }, handler);
    const canActivate = await guard.canActivate(context);
    expect(canActivate).toBe(false);
  });

  it("should throw UnauthorizedException on token validation error", async () => {
    Roles("some-role")(handler);
    jest.spyOn(reflector, "get").mockReturnValue(["some-role"]);
    (authService.validateToken as jest.Mock).mockRejectedValue(new Error("test error"));
    const context = mockExecutionContext({ authorization: "Bearer invalid-token" }, handler);
    await expect(guard.canActivate(context)).rejects.toThrow(new UnauthorizedException("Invalid token"));
  });

  it("should return false if payload is invalid", async () => {
    Roles("some-role")(handler);
    jest.spyOn(reflector, "get").mockReturnValue(["some-role"]);
    (authService.validateToken as jest.Mock).mockResolvedValue({ oid: "test-oid" });
    const context = mockExecutionContext({ authorization: "Bearer valid-token" }, handler);
    expect(await guard.canActivate(context)).toBe(false);
  });
});
