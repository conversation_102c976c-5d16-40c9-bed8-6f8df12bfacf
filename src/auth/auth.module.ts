import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { AuthGuard } from "./auth.guard";
import { AuthService } from "./auth.service";
import { RoleGuard } from "./role.guard";
import { PrismaService } from "@/prisma.service";

@Module({
  imports: [ConfigModule],
  providers: [AuthService, AuthGuard, RoleGuard, PrismaService],
  exports: [AuthService, AuthGuard, RoleGuard],
})
export class AuthModule {}
