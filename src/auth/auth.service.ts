import { Injectable, Logger, UnauthorizedException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as jwt from "jsonwebtoken";
import { JwksClient } from "jwks-rsa";
import { PrismaService } from "@/prisma.service";

@Injectable()
export class AuthService {
  private readonly jwksClient: JwksClient;
  private readonly logger = new Logger(AuthService.name);

  constructor(private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {
    this.jwksClient = new JwksClient({
      jwksUri: `https://login.microsoftonline.com/${this.configService.get<string>("AZURE_TENANT_ID")}/discovery/keys?appId=${this.configService.get<string>("AZURE_CLIENT_ID")}`,
    });
  }

  async validateToken(token: string) {
    const decoded = jwt.decode(token, { complete: true });
    if (!decoded?.header?.kid) {
      throw new Error("Invalid token");
    }

    const key = await this.jwksClient.getSigningKey(decoded.header.kid);
    const signingKey = key.getPublicKey();

    try {
      const payload = jwt.verify(token, signingKey, {
        audience: `api://${this.configService.get<string>("AZURE_CLIENT_ID")}`,
      }) as jwt.JwtPayload;

      const user = await this.prisma.user.findUnique({
        where: { email: payload.upn },
        include: {
          role: true,
        },
      });

      if (!user) {
        throw new UnauthorizedException("User not found");
      }

      payload.oid = user.id;
      payload.role = user.role.code;
      return payload;
    }
    catch (error) {
      this.logger.error("Error during token validation", error instanceof Error ? error.stack : String(error));
      throw new UnauthorizedException();
    }
  }
}
