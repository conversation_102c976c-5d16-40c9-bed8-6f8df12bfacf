import { ConfigService } from "@nestjs/config";
import { Reflector } from "@nestjs/core";
import { Test, TestingModule } from "@nestjs/testing";
import { AuthService } from "../auth/auth.service";
import { RoleGuard } from "../auth/role.guard";
import { ComparisonService } from "./comparison.service";
import { ComparisonRequestDto, ComparisonResponseDto } from "./dto";
import { FormulationController } from "./formulation.controller";
import { FormulationService } from "./formulation.service";

describe("FormulationController", () => {
  let controller: FormulationController;
  let comparisonService: ComparisonService;

  const mockFormulationService = {
    createFormulations: jest.fn(),
    getFormulations: jest.fn(),
    getFormulationById: jest.fn(),
  };

  const mockComparisonService = {
    getComparisons: jest.fn(),
  };

  const mockAuthService = {
    validateToken: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockReflector = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FormulationController],
      providers: [
        {
          provide: FormulationService,
          useValue: mockFormulationService,
        },
        {
          provide: ComparisonService,
          useValue: mockComparisonService,
        },
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: Reflector,
          useValue: mockReflector,
        },
        RoleGuard,
      ],
    }).compile();

    controller = module.get<FormulationController>(FormulationController);
    comparisonService = module.get<ComparisonService>(ComparisonService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });

  describe("createFormulations", () => {
    it("should create formulations successfully", async () => {
      const dto = { name: "Bumper", materials: [], testResults: [] };
      const mockRequest = { user: { oid: "user-1" } };
      const expectedResult = { data: [{ id: "form-1", name: "Bumper", grade: "A", ownerId: "user-1" }] };
      mockFormulationService.createFormulations.mockResolvedValue(expectedResult);
      const result = await controller.createFormulations(dto as any, mockRequest);
      expect(mockFormulationService.createFormulations).toHaveBeenCalledWith(dto, "user-1");
      expect(result).toEqual(expectedResult);
    });
    it("should throw error if userId not found", async () => {
      const dto = { name: "Bumper", materials: [], testResults: [] };
      const mockRequest = { user: {} };
      await expect(controller.createFormulations(dto as any, mockRequest)).rejects.toThrow("User ID not found in request");
    });
    it("should get userId from headers if not in user", async () => {
      const dto = { name: "Bumper", materials: [], testResults: [] };
      const mockRequest = { headers: { oid: "header-user-1" } };
      const expectedResult = { data: [{ id: "form-2", name: "Bumper", grade: "A", ownerId: "header-user-1" }] };
      mockFormulationService.createFormulations.mockResolvedValue(expectedResult);
      const result = await controller.createFormulations(dto as any, mockRequest);
      expect(mockFormulationService.createFormulations).toHaveBeenCalledWith(dto, "header-user-1");
      expect(result).toEqual(expectedResult);
    });
  });

  describe("compareFormulations", () => {
    it("should compare formulations with materialCriteria filter for ADMIN", async () => {
      const request: ComparisonRequestDto = {
        materialCriteria: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            minValue: 10,
            maxValue: 80,
          },
        ],
        page: 1,
        limit: 10,
      };

      const mockRequest = {
        user: {
          role: "ADMIN",
          oid: "admin-user-id",
        },
      };

      const expectedResult: ComparisonResponseDto = {
        data: [
          {
            formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
            name: "Bumper",
            grade: "A",
            isAccessible: true,
            materials: [
              {
                materialId: "94f114ca-6504-488f-9083-444b59b6177a",
                materialType: "Virgin PP Homopolymer",
                reference: "PP-H 1200",
                value: 70,
                searched: true,
              },
              {
                materialId: "another-uuid",
                materialType: "Recycle PP Homopolymer",
                reference: "PlastiLoop",
                value: 30,
                searched: false,
              },
            ],
            matchingMaterialsCount: 1,
            testResults: [
              {
                id: "test-result-1",
                testName: "Tensile Strength",
                standard: "ASTM D638",
                condition: "23°C, 50% RH",
                value: 25.5,
                minRange: 20,
                maxRange: 30,
              },
            ],
            tier: 1,
            optionalCriteriaMatched: 0,
            totalOptionalCriteria: 0,
          },
        ],
        meta: {
          total: 1,
          perPage: 10,
          currentPage: 1,
          lastPage: 1,
          from: 1,
          to: 1,
        },
      };

      mockComparisonService.getComparisons.mockResolvedValue(expectedResult);

      const result = await controller.compareFormulations(request, mockRequest);

      expect(comparisonService.getComparisons).toHaveBeenCalledWith(request, {
        userId: "admin-user-id",
        userRole: "ADMIN",
      });
      expect(comparisonService.getComparisons).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResult);
    });

    it("should compare formulations with ownership filtering for ENGINEERS", async () => {
      const request: ComparisonRequestDto = {
        materialCriteria: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            minValue: 10,
          },
        ],
        page: 1,
        limit: 10,
      };

      const mockRequest = {
        user: {
          role: "ENGINEERS",
          oid: "engineer-user-id",
        },
      };

      const expectedResult: ComparisonResponseDto = {
        data: [
          {
            formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
            name: "Bumper",
            grade: "A",
            isAccessible: false,
            materials: [], // Empty for non-owned formulation
            matchingMaterialsCount: 0,
            testResults: [],
            tier: 1,
            optionalCriteriaMatched: 0,
            totalOptionalCriteria: 0,
          },
        ],
        meta: {
          total: 1,
          perPage: 10,
          currentPage: 1,
          lastPage: 1,
          from: 1,
          to: 1,
        },
      };

      mockComparisonService.getComparisons.mockResolvedValue(expectedResult);

      const result = await controller.compareFormulations(request, mockRequest);

      expect(comparisonService.getComparisons).toHaveBeenCalledWith(request, {
        userId: "engineer-user-id",
        userRole: "ENGINEERS",
      });
      expect(comparisonService.getComparisons).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResult);
    });

    it("should handle request without materialCriteria", async () => {
      const request: ComparisonRequestDto = {
        page: 1,
        limit: 5,
      };

      const mockRequest = {
        user: {
          role: "DATA_SCIENTIST",
          oid: "data-scientist-id",
        },
      };

      const expectedResult: ComparisonResponseDto = {
        data: [],
        meta: {
          total: 0,
          perPage: 5,
          currentPage: 1,
          lastPage: 1,
          from: 0,
          to: 0,
        },
      };

      mockComparisonService.getComparisons.mockResolvedValue(expectedResult);

      const result = await controller.compareFormulations(request, mockRequest);

      expect(comparisonService.getComparisons).toHaveBeenCalledWith(request, {
        userId: "data-scientist-id",
        userRole: "DATA_SCIENTIST",
      });
      expect(result).toEqual(expectedResult);
    });

    it("should handle empty request for ENGINEERS", async () => {
      const request: ComparisonRequestDto = {};
      const mockRequest = {
        user: {
          role: "ENGINEERS",
          oid: "engineer-id",
        },
      };

      const expectedResult: ComparisonResponseDto = {
        data: [],
        meta: {
          total: 0,
          perPage: 10,
          currentPage: 1,
          lastPage: 1,
          from: 0,
          to: 0,
        },
      };

      mockComparisonService.getComparisons.mockResolvedValue(expectedResult);

      const result = await controller.compareFormulations(request, mockRequest);

      expect(comparisonService.getComparisons).toHaveBeenCalledWith(request, {
        userId: "engineer-id",
        userRole: "ENGINEERS",
      });
      expect(result).toEqual(expectedResult);
    });
  });

  describe("compareFormulations edge cases", () => {
    it("should map criteria.valueTo to value array", async () => {
      const request: any = { criteria: [{ value: 10, valueTo: 20 }] };
      const mockRequest = { user: { role: "ADMIN", oid: "admin-1" } };
      const expectedResult = { data: [] };
      mockComparisonService.getComparisons.mockResolvedValue(expectedResult);
      await controller.compareFormulations(request, mockRequest);
      expect(request.criteria[0].value).toEqual([10, 20]);
    });
    it("should handle request without user (undefined)", async () => {
      const request: any = { criteria: [] };
      const expectedResult = { data: [] };
      mockComparisonService.getComparisons.mockResolvedValue(expectedResult);
      const result = await controller.compareFormulations(request, {} as any);
      expect(mockComparisonService.getComparisons).toHaveBeenCalledWith(request, { userId: undefined, userRole: undefined });
      expect(result).toEqual(expectedResult);
    });
  });

  describe("getFormulationById", () => {
    it("should get formulation by ID for ADMIN user", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const mockRequest = {
        user: {
          role: "ADMIN",
          oid: "admin-user-id",
        },
      };

      const expectedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        name: "Bumper",
        grade: "A",
        ownerId: "owner-user-id",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
          {
            materialId: "another-uuid",
            materialType: "Recycle PP Homopolymer",
            reference: "PlastiLoop",
            value: 30,
          },
        ],
        testResults: [
          {
            id: "test-result-1",
            testName: "Tensile Strength",
            standard: "ASTM D638",
            condition: "23°C, 50% RH",
            value: 25.5,
            minRange: 20,
            maxRange: 30,
          },
        ],
      };

      mockFormulationService.getFormulationById.mockResolvedValue(expectedResult);

      const result = await controller.getFormulationById(formulationId, mockRequest);

      expect(mockFormulationService.getFormulationById).toHaveBeenCalledWith(
        formulationId,
        "ADMIN",
        "admin-user-id"
      );
      expect(mockFormulationService.getFormulationById).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResult);
    });

    it("should get formulation by ID for ENGINEERS user with ownership", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const mockRequest = {
        user: {
          role: "ENGINEERS",
          oid: "engineer-user-id",
        },
      };

      const expectedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        name: "Bumper",
        grade: "A",
        ownerId: "engineer-user-id",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
        ],
        testResults: [
          {
            id: "test-result-1",
            testName: "Tensile Strength",
            standard: "ASTM D638",
            condition: "23°C, 50% RH",
            value: 25.5,
            minRange: 20,
            maxRange: 30,
          },
        ],
      };

      mockFormulationService.getFormulationById.mockResolvedValue(expectedResult);

      const result = await controller.getFormulationById(formulationId, mockRequest);

      expect(mockFormulationService.getFormulationById).toHaveBeenCalledWith(
        formulationId,
        "ENGINEERS",
        "engineer-user-id"
      );
      expect(result).toEqual(expectedResult);
    });

    it("should get formulation by ID for ENGINEERS user with approved request", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const mockRequest = {
        user: {
          role: "ENGINEERS",
          oid: "engineer-user-id",
        },
      };

      const expectedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        name: "Bumper",
        grade: "A",
        ownerId: "other-user-id",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
        ],
        testResults: [
          {
            id: "test-result-1",
            testName: "Tensile Strength",
            standard: "ASTM D638",
            condition: "23°C, 50% RH",
            value: 25.5,
            minRange: 20,
            maxRange: 30,
          },
        ],
      };

      mockFormulationService.getFormulationById.mockResolvedValue(expectedResult);

      const result = await controller.getFormulationById(formulationId, mockRequest);

      expect(mockFormulationService.getFormulationById).toHaveBeenCalledWith(
        formulationId,
        "ENGINEERS",
        "engineer-user-id"
      );
      expect(result).toEqual(expectedResult);
    });

    it("should get formulation by ID for ENGINEERS user without access", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const mockRequest = {
        user: {
          role: "ENGINEERS",
          oid: "engineer-user-id",
        },
      };

      const expectedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        name: "Bumper",
        grade: "A",
        ownerId: "other-user-id",
        isAccessible: false,
        materials: [],
        testResults: [],
      };

      mockFormulationService.getFormulationById.mockResolvedValue(expectedResult);

      const result = await controller.getFormulationById(formulationId, mockRequest);

      expect(mockFormulationService.getFormulationById).toHaveBeenCalledWith(
        formulationId,
        "ENGINEERS",
        "engineer-user-id"
      );
      expect(result).toEqual(expectedResult);
    });

    it("should throw NotFoundException when formulation not found", async () => {
      const formulationId = "non-existent-id";
      const mockRequest = {
        user: {
          role: "ADMIN",
          oid: "admin-user-id",
        },
      };

      mockFormulationService.getFormulationById.mockResolvedValue(null);

      await expect(controller.getFormulationById(formulationId, mockRequest)).rejects.toThrow(
        `Formulation with ID ${formulationId} not found`
      );

      expect(mockFormulationService.getFormulationById).toHaveBeenCalledWith(
        formulationId,
        "ADMIN",
        "admin-user-id"
      );
    });

    it("should handle request without user information", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const mockRequest = {};

      const expectedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        name: "Bumper",
        grade: "A",
        ownerId: "owner-user-id",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
        ],
        testResults: [
          {
            id: "test-result-1",
            testName: "Tensile Strength",
            standard: "ASTM D638",
            condition: "23°C, 50% RH",
            value: 25.5,
            minRange: 20,
            maxRange: 30,
          },
        ],
      };

      mockFormulationService.getFormulationById.mockResolvedValue(expectedResult);

      const result = await controller.getFormulationById(formulationId, mockRequest);

      expect(mockFormulationService.getFormulationById).toHaveBeenCalledWith(
        formulationId,
        undefined,
        undefined
      );
      expect(result).toEqual(expectedResult);
    });
  });

  describe("getFormulations", () => {
    it("should return paginated formulations with all query parameters", async () => {
      const expectedResult = {
        data: [
          {
            formulationId: "form-1",
            name: "Bumper",
            grade: "A",
            ownerId: "owner-1",
            isAccessible: true,
            materials: [
              {
                materialId: "mat-1",
                materialType: "Virgin PP Homopolymer",
                reference: "PP-H 1200",
                value: 70,
              },
            ],
            testResults: [
              {
                id: "test-result-1",
                testName: "Tensile Strength",
                standard: "ASTM D638",
                condition: "23°C, 50% RH",
                value: 25.5,
                minRange: 20,
                maxRange: 30,
              },
            ],
          },
        ],
        meta: {
          total: 1,
          perPage: 10,
          currentPage: 1,
          lastPage: 1,
          from: 1,
          to: 1,
        },
      };

      mockFormulationService.getFormulations.mockResolvedValue(expectedResult);

      const filter = { search: "Bumper", page: 1, limit: 10 };
      const mockRequest = { user: { role: "ADMIN", oid: "owner-1" } };
      const result = await controller.getFormulations(filter, mockRequest);

      expect(mockFormulationService.getFormulations).toHaveBeenCalledWith(filter, "ADMIN", "owner-1");
      expect(result).toEqual(expectedResult);
    });

    it("should return paginated formulations with no filters", async () => {
      const expectedResult = {
        data: [],
        meta: {
          total: 0,
          perPage: 10,
          currentPage: 1,
          lastPage: 1,
          from: 0,
          to: 0,
        },
      };
      mockFormulationService.getFormulations.mockResolvedValue(expectedResult);
      const filter = {};
      const mockRequest = { user: { role: "ENGINEERS", oid: "engineer-1" } };
      const result = await controller.getFormulations(filter, mockRequest);
      expect(mockFormulationService.getFormulations).toHaveBeenCalledWith(filter, "ENGINEERS", "engineer-1");
      expect(result).toEqual(expectedResult);
    });

    it("should handle filter by ownerId and grade", async () => {
      const expectedResult = {
        data: [
          {
            formulationId: "form-2",
            name: "Dashboard",
            grade: "B",
            ownerId: "owner-2",
            isAccessible: true,
            materials: [],
            testResults: [],
          },
        ],
        meta: {
          total: 1,
          perPage: 5,
          currentPage: 2,
          lastPage: 1,
          from: 6,
          to: 10,
        },
      };
      mockFormulationService.getFormulations.mockResolvedValue(expectedResult);
      const filter = { ownerId: "owner-2", grade: "B", page: 2, limit: 5 };
      const mockRequest = { user: { role: "DATA_SCIENTIST", oid: "ds-1" } };
      const result = await controller.getFormulations(filter, mockRequest);
      expect(mockFormulationService.getFormulations).toHaveBeenCalledWith(filter, "DATA_SCIENTIST", "ds-1");
      expect(result).toEqual(expectedResult);
    });

    it("should handle undefined request user", async () => {
      const expectedResult = {
        data: [],
        meta: {
          total: 0,
          perPage: 10,
          currentPage: 1,
          lastPage: 1,
          from: 0,
          to: 0,
        },
      };
      mockFormulationService.getFormulations.mockResolvedValue(expectedResult);
      const filter = {};
      const result = await controller.getFormulations(filter);
      expect(mockFormulationService.getFormulations).toHaveBeenCalledWith(filter, undefined, undefined);
      expect(result).toEqual(expectedResult);
    });
  });
});
