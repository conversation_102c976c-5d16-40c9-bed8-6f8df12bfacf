import { Modu<PERSON> } from "@nestjs/common";
import { AuthModule } from "../auth/auth.module";
import { PrismaService } from "../prisma.service";
import { ComparisonService } from "./comparison.service";
import { FormulationController } from "./formulation.controller";
import { FormulationService } from "./formulation.service";
import { ComparisonRepository } from "./repositories/comparison.repository";
import { FormulationRepository } from "./repositories/formulation.repository";

@Module({
  imports: [AuthModule],
  controllers: [FormulationController],
  providers: [
    FormulationService,
    FormulationRepository,
    ComparisonService,
    ComparisonRepository,
    PrismaService,
  ],
  exports: [FormulationService, FormulationRepository, ComparisonService, ComparisonRepository],
})
export class FormulationModule {}
