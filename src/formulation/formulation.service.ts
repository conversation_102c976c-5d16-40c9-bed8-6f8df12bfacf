import { Injectable } from "@nestjs/common";
import { createPaginatedResponse, normalizePaginationParameters } from "../common/utils/pagination.util";
import { CreateFormulationDto, CreateFormulationResponseDto } from "./dto";
import { FormulationFilterDto } from "./dto/formulation-filter.dto";
import { PaginatedFormulationResponseDto } from "./dto/paginated-formulation-response.dto";
import { FormulationRepository } from "./repositories/formulation.repository";

@Injectable()
export class FormulationService {
  constructor(
    private readonly formulationRepository: FormulationRepository,
  ) {}

  async createFormulations(
    createFormulationDto: CreateFormulationDto,
    ownerId: string,
  ): Promise<CreateFormulationResponseDto> {
    const createdFormulations = await this.formulationRepository.createFormulations(
      createFormulationDto,
      ownerId,
    );

    return {
      data: createdFormulations,
    };
  }

  async getFormulations(
    filter: FormulationFilterDto,
    userRole?: string,
    userId?: string
  ): Promise<PaginatedFormulationResponseDto> {
    const { page, limit } = normalizePaginationParameters(filter.page, filter.limit);

    const rbacFilters = {
      ...filter,
      page,
      limit,
      userRole,
      userId,
    };

    const { formulations, total } = await this.formulationRepository.getFormulations(rbacFilters);

    return createPaginatedResponse({
      data: formulations,
      total,
      page,
      limit,
    });
  }

  async getFormulationById(
    id: string,
    userRole?: string,
    userId?: string
  ) {
    const formulation = await this.formulationRepository.getFormulationById(id);

    if (!formulation) {
      return null;
    }

    return this.formulationRepository.mapFormulation(formulation, userRole, userId);
  }
}
