import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsString, IsArray, IsNumber, IsOptional, ValidateNested, IsUUID, IsObject } from "class-validator";

export class CreateFormulationMaterialDto {
  @ApiProperty({
    description: "Material ID",
    example: "12253e96-c065-4a19-9904-9028d5eec93a",
  })
  @IsUUID()
  materialId: string;

  @ApiProperty({
    description: "Percentage of material per grade (grade as key, percentage as value)",
    example: { A: 20, B: 25, C: 0, D: 0, E: 15 },
  })
  @IsObject()
  grades: Record<string, number>;
}

export class TestResultGradeValueDto {
  @ApiProperty({
    description: "Test result value",
    example: 1200,
  })
  @IsNumber()
  value: number;

  @ApiProperty({
    description: "Minimum acceptable range for this grade",
    example: 1100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  minRange?: number;

  @ApiProperty({
    description: "Maximum acceptable range for this grade",
    example: 1300,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  maxRange?: number;
}

export class CreateFormulationTestResultDto {
  @ApiProperty({
    description: "Name of the test performed",
    example: "Tensile Strength",
  })
  @IsString()
  testName: string;

  @ApiProperty({
    description: "Testing standard used",
    example: "ASTM D638",
    required: false,
  })
  @IsOptional()
  @IsString()
  standard?: string;

  @ApiProperty({
    description: "Testing conditions",
    example: "23°C, 50% RH",
    required: false,
  })
  @IsOptional()
  @IsString()
  condition?: string;

  @ApiProperty({
    description: "Standardized property name identifier for this test",
    example: "tensileModulus",
  })
  @IsString()
  propertyName: string;

  @ApiProperty({
    description: "Test result values with individual min/max ranges per grade",
    example: {
      A: { value: 1200, minRange: 1100, maxRange: 1300 },
      B: { value: 1100, minRange: 1000, maxRange: 1200 },
      C: { value: 1150, minRange: 1050, maxRange: 1250 },
      D: { value: 1080, minRange: 980, maxRange: 1180 },
      E: { value: 1250, minRange: 1150, maxRange: 1350 },
    },
  })
  @IsObject()
  grades: Record<string, TestResultGradeValueDto>;
}

export class CreateFormulationDto {
  @ApiProperty({
    description: "Code for the formulation, typically a unique identifier",
    example: "EX-1234",
  })
  code: string;

  @ApiProperty({
    description: "Common name for all formulations",
    example: "Bumper Formulation",
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: "Array of materials with their percentages per grade",
    type: [CreateFormulationMaterialDto],
    example: [
      {
        materialId: "cf41949a-a3f8-4783-ae64-17e2d19bf2f2",
        grades: { A: 20 },
      },
      {
        materialId: "c4cfe815-9a20-45f6-b2ce-b8bcf1921e6d",
        grades: { A: 20 },
      },
      {
        materialId: "61f4570d-c25e-4142-8749-f269e70abcc2",
        grades: { A: 40 },
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateFormulationMaterialDto)
  materials: CreateFormulationMaterialDto[];

  @ApiProperty({
    description: "Array of test results with individual min/max ranges per grade",
    type: [CreateFormulationTestResultDto],
    example: [
      {
        testName: "Tensile modulus (MPa)",
        standard: "ISO 527-2",
        condition: "at 23°C",
        propertyName: "tensileModulus",
        grades: {
          A: { value: 1200, minRange: 1100, maxRange: 1300 },
        },
      },
      {
        testName: "MFI (g/10min)",
        standard: "ISO 1133",
        condition: "230°C 2,16kg",
        propertyName: "mfi",
        grades: {
          A: { value: 24, minRange: 22, maxRange: 26 },
        },
      },
      {
        testName: "Notched Izod (kJ/m²)",
        standard: "ISO 527",
        condition: "at 23°C, V Notch",
        propertyName: "nIzod",
        grades: {
          A: { value: 25.2, minRange: 23, maxRange: 27 },
        },
      },
      {
        testName: "Ash content (%)",
        standard: "ISO 3451-1",
        propertyName: "ashContent",
        grades: {
          A: { value: 25.1 },
        },
      },
      {
        testName: "Density (g/cm³)",
        standard: "ISO 1183",
        propertyName: "density",
        grades: {
          A: { value: 1.025 },
        },
      },
      {
        testName: "Price (€/ton)",
        propertyName: "price",
        grades: {
          A: { value: 1200 },
        },
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateFormulationTestResultDto)
  testResults: CreateFormulationTestResultDto[];
}
