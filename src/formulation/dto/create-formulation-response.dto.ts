import { ApiProperty } from "@nestjs/swagger";

export class CreatedFormulationDto {
  @ApiProperty({
    description: "Formulation ID",
    example: "uuid-1",
  })
  id: string;

  @ApiProperty({
    description: "Formulation name",
    example: "Bumper Formulation",
  })
  name: string;

  @ApiProperty({
    description: "Formulation grade",
    example: "A",
  })
  grade: string;

  @ApiProperty({
    description: "Owner ID",
    example: "user-uuid",
  })
  ownerId: string;
}

export class CreateFormulationResponseDto {
  @ApiProperty({
    description: "Array of created formulations - one for each grade from the compact format",
    type: [CreatedFormulationDto],
    example: [
      {
        id: "f1234567-89ab-cdef-0123-456789abcdef",
        name: "Bumper Formulation",
        grade: "A",
        ownerId: "user-uuid-123",
      },
      {
        id: "f2345678-9abc-def0-1234-56789abcdef0",
        name: "Bumper Formulation",
        grade: "B",
        ownerId: "user-uuid-123",
      },
      {
        id: "f3456789-abcd-ef01-2345-6789abcdef01",
        name: "Bumper Formulation",
        grade: "C",
        ownerId: "user-uuid-123",
      },
      {
        id: "f4567890-bcde-f012-3456-789abcdef012",
        name: "Bumper Formulation",
        grade: "D",
        ownerId: "user-uuid-123",
      },
      {
        id: "f5678901-cdef-0123-4567-89abcdef0123",
        name: "Bumper Formulation",
        grade: "E",
        ownerId: "user-uuid-123",
      },
    ],
  })
  data: CreatedFormulationDto[];
}
