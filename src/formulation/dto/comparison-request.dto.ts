import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsOptional, IsString, IsPositive, IsNumber, ValidateNested } from "class-validator";
import { CriteriaDto } from "./criteria.dto";

export class MaterialCriteriaDto {
  @ApiProperty({
    description: "Material ID",
    example: "uuid1",
  })
  @IsString()
  materialId: string;

  @ApiProperty({
    description: "Minimum percentage/value for this material",
    example: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  minValue?: number;

  @ApiProperty({
    description: "Maximum percentage/value for this material",
    example: 50,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  maxValue?: number;
}

export class ComparisonRequestDto {
  @ApiProperty({
    description: "Array of material criteria with optional min/max values",
    type: [MaterialCriteriaDto],
    required: false,
    example: [
      {
        materialId: "uuid1",
        minValue: 10,
        maxValue: 50,
      },
      {
        materialId: "uuid2",
        minValue: 5,
      },
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MaterialCriteriaDto)
  materialCriteria?: MaterialCriteriaDto[];

  @ApiProperty({
    description: "Page number for pagination",
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @IsPositive()
  page?: number = 1;

  @ApiProperty({
    description: "Number of items per page",
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @IsPositive()
  limit?: number = 10;

  @ApiProperty({
    description: "Array of test criteria to filter formulations",
    type: [CriteriaDto],
    required: false,
    example: [
      {
        propertyName: "mfi",
        operator: ">=",
        value: 5,
        required: true,
      },
      {
        propertyName: "density",
        operator: "<=",
        value: 1.2,
        required: false,
        tier: 1,
      },
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CriteriaDto)
  criteria?: CriteriaDto[];
}
