import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsEnum, Is<PERSON><PERSON>ber, IsOptional, IsString } from "class-validator";
import { CriteriaOperator } from "@/common/dto/criteria-operator.dto";

export class CriteriaDto {
  @ApiProperty({
    description: "Property name that matches specification property",
    example: "mfi",
  })
  @IsString()
  propertyName: string;

  @ApiProperty({
    description: "Tier/priority level for optional criteria only (1 = highest priority, 2 = second priority, etc.)",
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => value ? Number(value) : undefined)
  tier?: number;

  @ApiProperty({
    description: "Comparison operator",
    example: CriteriaOperator.GREATER_THAN_OR_EQUAL,
    enum: CriteriaOperator,
  })
  @IsEnum(CriteriaOperator)
  operator: CriteriaOperator;

  @ApiProperty({
    description: "Value",
    example: 5,
  })
  value: number | [number, number];

  @ApiProperty({
    description: "Value",
    example: 5,
  })
  valueTo: number;

  @ApiProperty({
    description: "Whether this criteria is required (true) or optional (false)",
    example: true,
  })
  @IsBoolean()
  required: boolean;
}
