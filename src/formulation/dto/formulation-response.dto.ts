import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";

export class FormulationMaterialDto {
  @ApiProperty({
    description: "Material ID",
    example: "94f114ca-6504-488f-9083-444b59b6177a",
  })
  materialId: string;

  @ApiProperty({
    description: "Material type name",
    example: "Virgin PP Homopolymer",
  })
  materialType: string;

  @ApiProperty({
    description: "Material reference code",
    example: "PP-H 1200",
  })
  reference: string;

  @ApiProperty({
    description: "Material percentage value in the formulation",
    example: 70,
  })
  value: number;
}

export class FormulationTestResultDto {
  @ApiProperty({
    description: "Test result ID",
    example: "test-result-1",
  })
  id: string;

  @ApiProperty({
    description: "Test name",
    example: "Tensile Strength",
  })
  testName: string;

  @ApiProperty({
    description: "Test standard",
    required: false,
    nullable: true,
    example: "ASTM D638",
  })
  @Transform(({ value }) => value === null ? undefined : value)
  standard?: string;

  @ApiProperty({
    description: "Test condition",
    required: false,
    nullable: true,
    example: "23°C, 50% RH",
  })
  @Transform(({ value }) => value === null ? undefined : value)
  condition?: string;

  @ApiProperty({
    description: "Test value",
    example: 25.5,
  })
  value: number;

  @ApiProperty({
    description: "Minimum range",
    required: false,
    nullable: true,
    example: 20,
  })
  @Transform(({ value }) => value === null ? undefined : value)
  minRange?: number;

  @ApiProperty({
    description: "Maximum range",
    required: false,
    nullable: true,
    example: 30,
  })
  @Transform(({ value }) => value === null ? undefined : value)
  maxRange?: number;
}

export class FormulationResponseDto {
  @ApiProperty({
    description: "Formulation ID",
    example: "35894c97-7fff-424f-82c5-f0550cf2faf1",
  })
  formulationId: string;

  @ApiProperty({
    description: "Formulation name",
    example: "Bumper",
  })
  name: string;

  @ApiProperty({
    description: "Formulation grade",
    example: "A",
  })
  grade: string;

  @ApiProperty({
    description: "Owner ID",
    example: "owner-user-id",
  })
  ownerId: string;

  @ApiProperty({
    description: "Indicates if the current user has access to view this formulation's materials and test results",
    example: true,
  })
  isAccessible: boolean;

  @ApiProperty({
    description: "Array of materials in this formulation",
    type: [FormulationMaterialDto],
  })
  materials: FormulationMaterialDto[];

  @ApiProperty({
    description: "Array of test results for this formulation",
    type: [FormulationTestResultDto],
  })
  testResults: FormulationTestResultDto[];
}
