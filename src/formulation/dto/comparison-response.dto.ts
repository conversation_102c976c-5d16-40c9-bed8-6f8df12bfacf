import { ApiProperty } from "@nestjs/swagger";
import { PaginationMetaDto } from "../../common/dto";

export class TestResultDto {
  @ApiProperty({
    description: "Test result ID",
    example: "12345678-1234-1234-1234-123456789abc",
  })
  id: string;

  @ApiProperty({
    description: "Name of the test performed",
    example: "Tensile Strength",
  })
  testName: string;

  @ApiProperty({
    description: "Test standard used",
    example: "ASTM D638",
    required: false,
  })
  standard: string | null;

  @ApiProperty({
    description: "Test condition",
    example: "23°C, 50% RH",
    required: false,
  })
  condition: string | null;

  @ApiProperty({
    description: "Test result value",
    example: 25.5,
  })
  value: number;

  @ApiProperty({
    description: "Minimum acceptable range",
    example: 20,
    required: false,
  })
  minRange?: number | null;

  @ApiProperty({
    description: "Maximum acceptable range",
    example: 30,
    required: false,
  })
  maxRange?: number | null;
}

export class MaterialInFormulationDto {
  @ApiProperty({
    description: "Material ID",
    example: "94f114ca-6504-488f-9083-444b59b6177a",
  })
  materialId: string;

  @ApiProperty({
    description: "Material type name",
    example: "Virgin PP Homopolymer",
  })
  materialType: string;

  @ApiProperty({
    description: "Material reference code",
    example: "PP-H 1200",
  })
  reference: string;

  @ApiProperty({
    description: "Material percentage value in the formulation",
    example: 70,
  })
  value: number;

  @ApiProperty({
    description: "Indicates if this material was included in the search criteria",
    example: true,
  })
  searched: boolean;
}

export class FormulationComparisonDto {
  @ApiProperty({
    description: "Formulation ID",
    example: "35894c97-7fff-424f-82c5-f0550cf2faf1",
  })
  formulationId: string;

  @ApiProperty({
    description: "Formulation name",
    example: "Bumper",
  })
  name: string;

  @ApiProperty({
    description: "Formulation grade",
    example: "A",
  })
  grade: string;

  @ApiProperty({
    description: "Indicates if the current user has access to view this formulation's materials and test results",
    example: true,
  })
  isAccessible: boolean;

  @ApiProperty({
    description: "Array of materials in this formulation with details",
    type: [MaterialInFormulationDto],
  })
  materials: MaterialInFormulationDto[];

  @ApiProperty({
    description: "Count of materials that match the search criteria",
    example: 1,
  })
  matchingMaterialsCount: number;

  @ApiProperty({
    description: "Array of test results for this formulation",
    type: [TestResultDto],
  })
  testResults: TestResultDto[];

  @ApiProperty({
    description: "Tier level based on optional criteria matching (1=highest, ascending numbers for lower tiers)",
    example: 1,
    required: false,
  })
  tier?: number;

  @ApiProperty({
    description: "Count of optional criteria matched",
    example: 3,
  })
  optionalCriteriaMatched: number;

  @ApiProperty({
    description: "Total count of optional criteria in the request",
    example: 5,
  })
  totalOptionalCriteria: number;
}

export class ComparisonResponseDto {
  @ApiProperty({
    description: "List of formulation comparisons",
    type: [FormulationComparisonDto],
  })
  data: FormulationComparisonDto[];

  @ApiProperty({
    description: "Pagination metadata",
    type: PaginationMetaDto,
  })
  meta: PaginationMetaDto;
}
