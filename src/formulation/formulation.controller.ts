import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  Request,
  Req,
  Param,
  NotFoundException,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiOAuth2,
  <PERSON>piParam,
} from "@nestjs/swagger";
import { AuthGuard } from "../auth/auth.guard";
import { RoleGuard, Roles } from "../auth/role.guard";
import { UserRole } from "../role/role.types";
import { ComparisonService } from "./comparison.service";
import {
  CreateFormulationDto,
  CreateFormulationResponseDto,
  FormulationFilterDto,
  PaginatedFormulationResponseDto,
  FormulationResponseDto,
  ComparisonRequestDto,
  ComparisonResponseDto,
} from "./dto";
import { FormulationService } from "./formulation.service";

@ApiBearerAuth()
@ApiOAuth2([process.env.AZURE_API_SCOPE!])
@UseGuards(AuthGuard, RoleGuard)
@ApiTags("formulations")
@Controller("formulations")
export class FormulationController {
  constructor(
    private readonly formulationService: FormulationService,
    private readonly comparisonService: ComparisonService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @Roles(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
  )
  @ApiOperation({
    summary: "Create multiple formulations with materials and test results",
    description:
      "Create multiple formulations using a compact format. Materials and test results are organized by grades as key-value pairs to eliminate redundancy. Each material specifies its percentage for each grade, and test results specify values for each grade. Only materials with non-zero percentages are included in each formulation. All materials must exist in the database. Access is role-based: Admin, Data Scientist, Engineering Manager, and Engineers can create formulations.",
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: "Formulations created successfully. Each grade (A, B, C, D, E) becomes a separate formulation with only non-zero percentage materials included.",
    type: CreateFormulationResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: "Bad request - Invalid data or materials not found",
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: "Unauthorized - Invalid or missing token",
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: "Forbidden - Insufficient permissions for your role",
  })
  async createFormulations(
    @Body() createFormulationDto: CreateFormulationDto,
    @Request() request: any,
  ): Promise<CreateFormulationResponseDto> {
    const userId = request.user?.oid || request.headers?.oid;

    if (!userId) {
      throw new Error("User ID not found in request");
    }

    return this.formulationService.createFormulations(
      createFormulationDto,
      userId,
    );
  }

  @Get()
  @Roles(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
  )
  @ApiOperation({
    summary: "Get formulations with filtering",
    description: "Get paginated list of formulations with optional filters. Supports search across name and grade fields, individual name/grade filtering, and ownerId filtering. Supports pagination. Engineers can only see formulations they own or have approved access requests.",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Formulations retrieved successfully",
    type: PaginatedFormulationResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: "Unauthorized - Invalid or missing token",
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: "Forbidden - Insufficient permissions for your role",
  })
  async getFormulations(
    @Query() filter: FormulationFilterDto,
    @Req() request?: any,
  ): Promise<PaginatedFormulationResponseDto> {
    const userRole = request?.user?.role;
    const userId = request?.user?.oid;

    return this.formulationService.getFormulations(filter, userRole, userId);
  }

  @Get(":id")
  @Roles(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
  )
  @ApiOperation({
    summary: "Get a single formulation by ID",
    description: "Retrieve a single formulation by its unique ID. Access is role-based: Admin, Data Scientist, Engineering Manager, and Engineers can view formulations.",
  })
  @ApiParam({ name: "id", type: String, description: "Formulation ID" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Formulation retrieved successfully",
    type: FormulationResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: "Formulation not found",
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: "Unauthorized - Invalid or missing token",
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: "Forbidden - Insufficient permissions for your role",
  })
  async getFormulationById(
    @Param("id") id: string,
    @Req() request?: any,
  ): Promise<FormulationResponseDto> {
    const userRole = request?.user?.role;
    const userId = request?.user?.oid;

    const formulation = await this.formulationService.getFormulationById(id, userRole, userId);
    if (!formulation) {
      throw new NotFoundException(`Formulation with ID ${id} not found`);
    }
    return formulation;
  }

  @Post("compare")
  @HttpCode(200)
  @Roles(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
  )
  @ApiOperation({
    summary: "Compare materials based on formulations",
    description:
      "Compare materials based on formulations. Returns formulations sorted by matching material count with their test results. Input materialId array is optional. Materials that were searched will be marked with 'searched: true'. Each formulation includes test results from the testResults array. Access is role-based: Admin, Data Scientist, and Engineering Manager have full access. Engineers have conditional access. Feedstock & Recycling Members and Material Manager are restricted from this endpoint.",
  })
  @ApiResponse({
    status: 200,
    description: "Material comparisons retrieved successfully",
    type: ComparisonResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: "Unauthorized - Invalid or missing token",
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions for your role",
  })
  async compareFormulations(
    @Body() request: ComparisonRequestDto,
    @Request() request_: any,
  ): Promise<ComparisonResponseDto> {
    const userRole = request_.user?.role || request_.headers?.role;
    const userId = request_.user?.oid || request_.headers?.oid;

    request.criteria?.map((c) => {
      if (c.valueTo) {
        c.value = [Number(c.value), c.valueTo];
      }
    });

    return this.comparisonService.getComparisons(request, { userId, userRole });
  }
}
