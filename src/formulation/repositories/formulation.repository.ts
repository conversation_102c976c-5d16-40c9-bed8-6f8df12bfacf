import { BadRequestException, Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma.service";
import { CreateFormulationDto } from "../dto";
import { FormulationFilterDto } from "../dto/formulation-filter.dto";
import { AccessStatus } from "@/formulation/enum/access-status.enum";
import { FormulationAccess } from "@/formulation/utils/formulation-access";

export interface FormulationWithDetails {
  id: string
  name: string
  grade: string
  ownerId: string
  materials: {
    materialId: string
    percentage: number
    material: {
      type: string
      reference: string
    }
  }[]
  testResults: {
    id: string
    testName: string
    standard: string | null
    condition: string | null
    value: number
    minRange: number | null
    maxRange: number | null
  }[]
  requests: {
    id: string
    requesterId: string
    status: string
  }[]
}

export interface FormulationFilters extends FormulationFilterDto {
  userRole?: string
  userId?: string
}

@Injectable()
export class FormulationRepository {
  public constructor(private readonly prisma: PrismaService) {}

  public async createFormulations(createFormulationDto: CreateFormulationDto, ownerId: string) {
    const { code, name, materials, testResults } = createFormulationDto;

    const allGrades = materials.length > 0 ? Object.keys(materials[0].grades) : [];

    const materialIds = materials.map(m => m.materialId);
    const existingMaterials = await this.prisma.material.findMany({
      select: { id: true },
      where: { id: { in: materialIds } },
    });

    if (existingMaterials.length !== materialIds.length) {
      const existingIds = new Set(existingMaterials.map(m => m.id));
      const missingIds = materialIds.filter(id => !existingIds.has(id));
      throw new BadRequestException(`Materials not found: ${missingIds.join(", ")}`);
    }

    const formulations = allGrades.map((grade) => {
      const formulationMaterials = materials
        .filter(m => m.grades[grade] && m.grades[grade] > 0)
        .map(m => ({
          materialId: m.materialId,
          percentage: m.grades[grade],
        }));

      const formulationTestResults = testResults.map((tr) => {
        const gradeData = tr.grades[grade];
        return {
          condition: tr.condition,
          maxRange: gradeData.maxRange,
          minRange: gradeData.minRange,
          propertyName: tr.propertyName,
          standard: tr.standard,
          testName: tr.testName,
          value: gradeData.value,
        };
      });

      return {
        grade,
        materials: formulationMaterials,
        testResults: formulationTestResults,
      };
    });

    return this.prisma.$transaction(async (tx) => {
      const createdFormulations: {
        id: string
        code: string
        name: string
        grade: string
        ownerId: string
      }[] = [];

      for (const formulation of formulations) {
        const createdFormulation = await tx.formulation.create({
          data: {
            code,
            grade: formulation.grade,
            materials: {
              create: formulation.materials.map(m => ({
                materialId: m.materialId,
                percentage: m.percentage,
              })),
            },
            name,
            ownerId,
            testResults: {
              create: formulation.testResults.map(tr => ({
                condition: tr.condition,
                maxRange: tr.maxRange,
                minRange: tr.minRange,
                propertyName: tr.propertyName,
                standard: tr.standard,
                testName: tr.testName,
                value: tr.value,
              })),
            },
          },
          select: {
            code: true,
            grade: true,
            id: true,
            name: true,
            ownerId: true,
          },
        });

        createdFormulations.push(createdFormulation);
      }

      return createdFormulations;
    });
  }

  private buildWhereClause(filter: FormulationFilters): Record<string, unknown> {
    const { name, grade, ownerId } = filter;
    const where: Record<string, unknown> = {};

    if (name) {
      where.name = { contains: name, mode: "insensitive" };
    }
    if (grade) {
      where.grade = grade;
    }
    if (ownerId) {
      where.ownerId = ownerId;
    }

    return where;
  }

  private getFormulationSelect() {
    return {
      grade: true,
      id: true,
      materials: {
        select: {
          material: {
            select: {
              reference: true,
              type: true,
            },
          },
          materialId: true,
          percentage: true,
        },
      },
      name: true,
      ownerId: true,
      requests: {
        select: {
          id: true,
          requesterId: true,
          status: true,
        },
      },
      testResults: {
        select: {
          condition: true,
          id: true,
          maxRange: true,
          minRange: true,
          standard: true,
          testName: true,
          value: true,
        },
      },
    };
  }

  public async getFormulations(filter: FormulationFilters) {
    const { search, page = 1, limit = 10, userRole, userId } = filter;
    const skip = (page - 1) * limit;

    const formulationSelect = this.getFormulationSelect();

    const where = this.buildWhereClause(filter);

    const formulations = await this.prisma.formulation.findMany({
      orderBy: { name: "asc" },
      select: formulationSelect,
      where,
    });

    let filtered = formulations;
    if (search) {
      const searchLower = search.toLowerCase();
      filtered = formulations.filter((f) => {
        const combined = `${f.name} ${f.grade}`.toLowerCase();
        const matches = combined.includes(searchLower);
        return matches;
      });
    }

    const paged = filtered.slice(skip, skip + limit);

    return {
      formulations: paged.map(f => this.mapFormulation(f, userRole, userId)),
      total: filtered.length,
    };
  }

  public async getFormulationById(id: string): Promise<FormulationWithDetails | null> {
    const formulation = await this.prisma.formulation.findUnique({
      select: this.getFormulationSelect(),
      where: { id },
    });

    return formulation;
  }

  public mapFormulation(formulation: FormulationWithDetails, userRole?: string, userId?: string) {
    const accessStatus = this.determineAccessStatus(formulation, userRole, userId);
    const isAccessible = accessStatus === AccessStatus.ACCESSED;

    return {
      accessStatus,
      formulationId: formulation.id,
      grade: formulation.grade,
      isAccessible,
      materials: isAccessible
        ? formulation.materials.map(m => ({
            materialId: m.materialId,
            materialType: m.material.type,
            reference: m.material.reference,
            value: m.percentage,
          }))
        : [],
      name: formulation.name,
      ownerId: formulation.ownerId,
      testResults: isAccessible
        ? formulation.testResults.map(tr => ({
            condition: tr.condition ?? undefined,
            id: tr.id,
            maxRange: tr.maxRange ?? undefined,
            minRange: tr.minRange ?? undefined,
            standard: tr.standard ?? undefined,
            testName: tr.testName,
            value: tr.value,
          }))
        : [],
    };
  }

  private determineAccessStatus(
    formulation: FormulationWithDetails,
    userRole?: string,
    userId?: string
  ): AccessStatus {
    if (!userId) {
      return AccessStatus.REQUIRED;
    }

    const isFormulationOwner = FormulationAccess.isFormulationOwner(formulation.ownerId, userId);
    const hasFullAccessRole = FormulationAccess.hasFullAccessRole(userRole);

    if (isFormulationOwner || hasFullAccessRole) {
      return AccessStatus.ACCESSED;
    }

    const userAccessRequest = formulation.requests.find(
      request => request.requesterId === userId
    );

    if (!userAccessRequest) {
      return AccessStatus.REQUIRED;
    }

    return FormulationAccess.mapRequestStatusToAccessStatus(userAccessRequest.status);
  }
}
