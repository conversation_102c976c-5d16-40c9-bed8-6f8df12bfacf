import { CriteriaDto, MaterialCriteriaDto } from "../dto";
import { CriteriaOperator } from "@/common/dto/criteria-operator.dto";

export class CriteriaQueryBuilder {
  private paramIndex = 0;

  getNextParamIndex(): number {
    return ++this.paramIndex;
  }

  buildCompleteQuery(
    requiredCriteria?: CriteriaDto[],
    optionalCriteria?: CriteriaDto[],
    materialCriteria?: MaterialCriteriaDto[],
  ): string {
    const materialCondition = this.buildMaterialCondition(materialCriteria);
    const requiredCondition = this.buildRequiredCriteriaCondition(requiredCriteria);

    const optionalCaseStatements = optionalCriteria?.length
      ? optionalCriteria.map(criteria =>
          `CASE WHEN EXISTS (SELECT 1 FROM "test_result" tr WHERE tr."formulation_id" = f.id AND tr."property_name" = $${this.getNextParamIndex()} AND ${this.buildSqlValueCondition(criteria)}) THEN 1 ELSE 0 END`
        ).join(" + ")
      : "0";

    const tierBasedScoring = optionalCriteria?.length
      ? optionalCriteria.map((criteria) => {
          const weight = criteria.tier ? Math.max(1, 11 - criteria.tier) : 5;
          return `CASE WHEN EXISTS (SELECT 1 FROM "test_result" tr WHERE tr."formulation_id" = f.id AND tr."property_name" = $${this.getNextParamIndex()} AND ${this.buildSqlValueCondition(criteria)}) THEN ${weight} ELSE 0 END`;
        }).join(" + ")
      : "0";

    const materialCountCase = materialCriteria?.length ? this.buildMaterialCountSubquery(materialCriteria) : "0";

    return `
      WITH ranked_formulations AS (
        SELECT 
          f.id,
          f.name,
          f.grade,
          f."owner_id",
          (${optionalCaseStatements}) as optional_matches,
          (${tierBasedScoring}) as tier_score,
          ${materialCountCase} as material_matches,
          RANK() OVER (ORDER BY (${tierBasedScoring}) DESC, ${materialCountCase} DESC, f.name ASC) as tier
        FROM "formulation" f
        WHERE 1=1 ${materialCondition} ${requiredCondition}
      ),
      formulation_materials AS (
        SELECT 
          rf.id as formulation_id,
          COALESCE(
            JSON_AGG(
              JSON_BUILD_OBJECT(
                'materialId', fm."material_id",
                'percentage', fm.percentage,
                'type', m.material_type,
                'reference', m.material_reference
              )
            ) FILTER (WHERE fm.id IS NOT NULL), 
            '[]'::json
          ) as materials
        FROM ranked_formulations rf
        LEFT JOIN "formulation_material" fm ON rf.id = fm."formulation_id"
        LEFT JOIN "material" m ON fm."material_id" = m.id
        GROUP BY rf.id
      ),
      formulation_test_results AS (
        SELECT 
          rf.id as formulation_id,
          COALESCE(
            JSON_AGG(
              JSON_BUILD_OBJECT(
                'id', tr.id,
                'testName', tr."test_name",
                'propertyName', tr."property_name",
                'standard', tr.standard,
                'condition', tr.condition,
                'value', tr.value,
                'minRange', tr."min_range",
                'maxRange', tr."max_range"
              )
            ) FILTER (WHERE tr.id IS NOT NULL),
            '[]'::json
          ) as test_results
        FROM ranked_formulations rf
        LEFT JOIN "test_result" tr ON rf.id = tr."formulation_id"
        GROUP BY rf.id
      )
      SELECT 
        rf.*,
        fm.materials,
        ftr.test_results
      FROM ranked_formulations rf
      LEFT JOIN formulation_materials fm ON rf.id = fm.formulation_id
      LEFT JOIN formulation_test_results ftr ON rf.id = ftr.formulation_id
      ORDER BY rf.tier ASC, rf.material_matches DESC, rf.name ASC
      OFFSET $${this.getNextParamIndex()} LIMIT $${this.getNextParamIndex()}
    `;
  }

  buildCountQuery(requiredCriteria?: CriteriaDto[], materialCriteria?: MaterialCriteriaDto[]): string {
    const materialCondition = this.buildMaterialCondition(materialCriteria);
    const requiredCondition = this.buildRequiredCriteriaCondition(requiredCriteria);

    return `
      SELECT COUNT(*) as count
      FROM "formulation" f
      WHERE 1=1 ${materialCondition} ${requiredCondition}
    `;
  }

  private buildMaterialCondition(materialCriteria?: MaterialCriteriaDto[]): string {
    return materialCriteria?.length
      ? `AND (${materialCriteria.map((mc) => {
        let condition = `EXISTS (SELECT 1 FROM "formulation_material" fm WHERE fm."formulation_id" = f.id AND fm."material_id" = $${this.getNextParamIndex()}`;
        if (mc.minValue !== undefined) {
          condition += ` AND fm.percentage >= $${this.getNextParamIndex()}`;
        }
        if (mc.maxValue !== undefined) {
          condition += ` AND fm.percentage <= $${this.getNextParamIndex()}`;
        }
        condition += ")";
        return condition;
      }).join(" OR ")})`
      : "";
  }

  private buildRequiredCriteriaCondition(requiredCriteria?: CriteriaDto[]): string {
    return requiredCriteria?.length
      ? `AND (${requiredCriteria.map(criteria =>
        `EXISTS (SELECT 1 FROM "test_result" tr WHERE tr."formulation_id" = f.id AND tr."property_name" = $${this.getNextParamIndex()} AND ${this.buildSqlValueCondition(criteria)})`
      ).join(" OR ")})`
      : "";
  }

  private buildSqlValueCondition(criteria: CriteriaDto): string {
    if (criteria.operator === CriteriaOperator.BETWEEN) {
      return `tr.value::numeric BETWEEN $${this.getNextParamIndex()}::numeric AND $${this.getNextParamIndex()}::numeric`;
    }
    return `tr.value::numeric ${criteria.operator} $${this.getNextParamIndex()}::numeric`;
  }

  private buildMaterialCountSubquery(materialCriteria: MaterialCriteriaDto[]): string {
    const parameterPlaceholders = materialCriteria.map(() => `$${this.getNextParamIndex()}`).join(", ");
    return `(SELECT COUNT(DISTINCT fm."material_id") FROM "formulation_material" fm WHERE fm."formulation_id" = f.id AND fm."material_id" IN (${parameterPlaceholders}))`;
  }

  buildQueryParams(
    requiredCriteria?: CriteriaDto[],
    optionalCriteria?: CriteriaDto[],
    skip = 0,
    limit = 10,
    materialCriteria?: MaterialCriteriaDto[],
  ): any[] {
    const parameters: any[] = [];

    this.addMaterialCriteriaParams(parameters, materialCriteria);
    this.addCriteriaParams(parameters, requiredCriteria);
    this.addCriteriaParams(parameters, optionalCriteria);
    this.addCriteriaParams(parameters, optionalCriteria);

    if (materialCriteria?.length) {
      for (const mc of materialCriteria) {
        parameters.push(mc.materialId);
      }
    }

    parameters.push(skip, limit);

    return parameters;
  }

  private addMaterialCriteriaParams(parameters: any[], materialCriteria?: MaterialCriteriaDto[]): void {
    if (materialCriteria?.length) {
      for (const mc of materialCriteria) {
        parameters.push(mc.materialId);
        if (mc.minValue !== undefined) {
          parameters.push(mc.minValue);
        }
        if (mc.maxValue !== undefined) {
          parameters.push(mc.maxValue);
        }
      }
    }
  }

  private addCriteriaParams(parameters: any[], criteria?: CriteriaDto[]): void {
    if (criteria) {
      for (const c of criteria) {
        parameters.push(c.propertyName);
        if (c.operator === CriteriaOperator.BETWEEN) {
          const [min, max] = c.value as [number, number];
          parameters.push(min, max);
        }
        else {
          parameters.push(c.value);
        }
      }
    }
  }

  buildCountParams(requiredCriteria?: CriteriaDto[], materialCriteria?: MaterialCriteriaDto[]): any[] {
    const parameters: any[] = [];

    this.addMaterialCriteriaParams(parameters, materialCriteria);
    this.addCriteriaParams(parameters, requiredCriteria);

    return parameters;
  }
}
