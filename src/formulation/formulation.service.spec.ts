import { Test, TestingModule } from "@nestjs/testing";
import { UserRole } from "../role/role.types";
import { FormulationService } from "./formulation.service";
import { FormulationRepository } from "./repositories/formulation.repository";

const mockFormulationRepository = {
  createFormulations: jest.fn(),
  getFormulations: jest.fn(),
  getFormulationById: jest.fn(),
  mapFormulation: jest.fn(),
};

describe("FormulationService", () => {
  let service: FormulationService;
  let repository: FormulationRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FormulationService,
        {
          provide: FormulationRepository,
          useValue: mockFormulationRepository,
        },
      ],
    }).compile();

    service = module.get<FormulationService>(FormulationService);
    repository = module.get<FormulationRepository>(FormulationRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("getFormulationById", () => {
    it("should return formulation when found", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const userRole = "ADMIN";
      const userId = "admin-user-id";

      const mockFormulation = {
        id: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        name: "Bumper",
        grade: "A",
        ownerId: "owner-user-id",
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            percentage: 70,
            material: {
              type: "Virgin PP Homopolymer",
              reference: "PP-H 1200",
            },
          },
        ],
        testResults: [
          {
            id: "test-result-1",
            testName: "Tensile Strength",
            standard: "ASTM D638",
            condition: "23°C, 50% RH",
            value: 25.5,
            minRange: 20,
            maxRange: 30,
          },
        ],
        requests: [],
      };

      const expectedMappedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        name: "Bumper",
        grade: "A",
        ownerId: "owner-user-id",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
        ],
        testResults: [
          {
            id: "test-result-1",
            testName: "Tensile Strength",
            standard: "ASTM D638",
            condition: "23°C, 50% RH",
            value: 25.5,
            minRange: 20,
            maxRange: 30,
          },
        ],
      };

      mockFormulationRepository.getFormulationById.mockResolvedValue(mockFormulation);
      mockFormulationRepository.mapFormulation.mockReturnValue(expectedMappedResult);

      const result = await service.getFormulationById(formulationId, userRole, userId);

      expect(repository.getFormulationById).toHaveBeenCalledWith(formulationId);
      expect(repository.mapFormulation).toHaveBeenCalledWith(mockFormulation, userRole, userId);
      expect(result).toEqual(expectedMappedResult);
    });

    it("should return null when formulation not found", async () => {
      const formulationId = "non-existent-id";
      const userRole = "ADMIN";
      const userId = "admin-user-id";

      mockFormulationRepository.getFormulationById.mockResolvedValue(null);

      const result = await service.getFormulationById(formulationId, userRole, userId);

      expect(repository.getFormulationById).toHaveBeenCalledWith(formulationId);
      expect(repository.mapFormulation).not.toHaveBeenCalled();
      expect(result).toBeNull();
    });

    it("should handle ENGINEERS role with ownership", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const userRole = UserRole.ENGINEERS;
      const userId = "engineer-user-id";

      const mockFormulation = {
        id: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        name: "Bumper",
        grade: "A",
        ownerId: "engineer-user-id",
        materials: [],
        testResults: [],
        requests: [],
      };

      const expectedMappedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        name: "Bumper",
        grade: "A",
        ownerId: "engineer-user-id",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
        ],
        testResults: [
          {
            id: "test-result-1",
            testName: "Tensile Strength",
            standard: "ASTM D638",
            condition: "23°C, 50% RH",
            value: 25.5,
            minRange: 20,
            maxRange: 30,
          },
        ],
      };

      mockFormulationRepository.getFormulationById.mockResolvedValue(mockFormulation);
      mockFormulationRepository.mapFormulation.mockReturnValue(expectedMappedResult);

      const result = await service.getFormulationById(formulationId, userRole, userId);

      expect(repository.getFormulationById).toHaveBeenCalledWith(formulationId);
      expect(repository.mapFormulation).toHaveBeenCalledWith(mockFormulation, userRole, userId);
      expect(result).toEqual(expectedMappedResult);
    });

    it("should handle ENGINEERS role without access", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const userRole = UserRole.ENGINEERS;
      const userId = "engineer-user-id";

      const mockFormulation = {
        id: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        name: "Bumper",
        grade: "A",
        ownerId: "other-user-id",
        materials: [],
        testResults: [],
        requests: [],
      };

      const expectedMappedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        name: "Bumper",
        grade: "A",
        ownerId: "other-user-id",
        isAccessible: false,
        materials: [], // Empty because no access
        testResults: [], // Empty because no access
      };

      mockFormulationRepository.getFormulationById.mockResolvedValue(mockFormulation);
      mockFormulationRepository.mapFormulation.mockReturnValue(expectedMappedResult);

      const result = await service.getFormulationById(formulationId, userRole, userId);

      expect(repository.getFormulationById).toHaveBeenCalledWith(formulationId);
      expect(repository.mapFormulation).toHaveBeenCalledWith(mockFormulation, userRole, userId);
      expect(result).toEqual(expectedMappedResult);
    });

    it("should handle undefined user role and userId", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const userRole = undefined;
      const userId = undefined;

      const mockFormulation = {
        id: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        name: "Bumper",
        grade: "A",
        ownerId: "owner-user-id",
        materials: [],
        testResults: [],
        requests: [],
      };

      const expectedMappedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        name: "Bumper",
        grade: "A",
        ownerId: "owner-user-id",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
        ],
        testResults: [
          {
            id: "test-result-1",
            testName: "Tensile Strength",
            standard: "ASTM D638",
            condition: "23°C, 50% RH",
            value: 25.5,
            minRange: 20,
            maxRange: 30,
          },
        ],
      };

      mockFormulationRepository.getFormulationById.mockResolvedValue(mockFormulation);
      mockFormulationRepository.mapFormulation.mockReturnValue(expectedMappedResult);

      const result = await service.getFormulationById(formulationId, userRole, userId);

      expect(repository.getFormulationById).toHaveBeenCalledWith(formulationId);
      expect(repository.mapFormulation).toHaveBeenCalledWith(mockFormulation, userRole, userId);
      expect(result).toEqual(expectedMappedResult);
    });
  });

  describe("createFormulations", () => {
    it("should create formulations and return response", async () => {
      const dto = { name: "Bumper", materials: [], testResults: [] };
      const ownerId = "user-1";
      const created = [{ id: "form-1", name: "Bumper", grade: "A", ownerId: "user-1" }];
      mockFormulationRepository.createFormulations.mockResolvedValue(created);
      const result = await service.createFormulations(dto as any, ownerId);
      expect(repository.createFormulations).toHaveBeenCalledWith(dto, ownerId);
      expect(result).toEqual({ data: created });
    });
  });

  describe("getFormulations", () => {
    it("should return paginated formulations", async () => {
      const filter = { search: "Bumper", page: 1, limit: 10 };
      const userRole = "ADMIN";
      const userId = "user-1";
      const repoResult = {
        formulations: [
          {
            formulationId: "form-1",
            name: "Bumper",
            grade: "A",
            ownerId: "user-1",
            isAccessible: true,
            materials: [],
            testResults: [],
          },
        ],
        total: 1,
      };
      mockFormulationRepository.getFormulations.mockResolvedValue(repoResult);
      const result = await service.getFormulations(filter as any, userRole, userId);
      expect(repository.getFormulations).toHaveBeenCalledWith({ ...filter, page: 1, limit: 10, userRole, userId });
      expect(result.data).toHaveLength(1);
      expect(result.meta.total).toBe(1);
    });

    it("should return empty paginated result", async () => {
      const filter = { page: 1, limit: 10 };
      mockFormulationRepository.getFormulations.mockResolvedValue({ formulations: [], total: 0 });
      const result = await service.getFormulations(filter as any);
      expect(result.data).toHaveLength(0);
      expect(result.meta.total).toBe(0);
    });
  });
});
