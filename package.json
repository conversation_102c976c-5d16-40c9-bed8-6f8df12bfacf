{"name": "materiact-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "npx prisma generate && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix && prisma format", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky", "prisma": "prisma"}, "dependencies": {"@azure/communication-email": "^1.0.0", "@azure/monitor-opentelemetry-exporter": "^1.0.0-beta.32", "@fastify/static": "^8.2.0", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/event-emitter": "^3.0.1", "@nestjs/platform-fastify": "^11.1.3", "@nestjs/swagger": "^11.2.0", "@opentelemetry/auto-instrumentations-node": "^0.60.1", "@opentelemetry/sdk-logs": "^0.202.0", "@opentelemetry/sdk-node": "^0.202.0", "@opentelemetry/winston-transport": "^0.13.0", "@prisma/client": "^6.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "nest-winston": "^1.10.2", "prisma-extension-soft-delete": "^2.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@stylistic/eslint-plugin": "^4.4.1", "@swc/cli": "^0.7.7", "@swc/core": "^1.12.1", "@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^24.0.1", "@types/supertest": "^6.0.3", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsdoc": "^51.0.1", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-sort-class-members": "^1.21.0", "eslint-plugin-unicorn": "^59.0.1", "globals": "^16.2.0", "husky": "^9.1.7", "jest": "^29.7.0", "prettier": "^3.5.3", "prisma": "^6.9.0", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.34.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@/generated/(.*)$": "<rootDir>/../generated/$1", "^@/(.*)$": "<rootDir>/$1"}}, "prisma": {"seed": "ts-node prisma/seed.ts"}}